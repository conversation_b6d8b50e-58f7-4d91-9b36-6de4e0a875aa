<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="059519b0-c8ec-4cb9-8853-df06566cad9a" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/控制.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.vscode/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/11.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/12.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/13.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/14.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/2.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/4.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/5.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/6.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/8.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/HTML解码链接提取结果.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README_右键菜单安装说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/actors_dict.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/avs刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/image_handle-1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/javbus无码-1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/javdb刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/javdb无码刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/rename_video.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/strings.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/tdl前端.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/tdl配套程序.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/以相机型号创建子文件夹并移动图片.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/删除小图片.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/删除杂项文件移动小图片.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/合并wmv.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/图片预览查看管理器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/多重子文件夹的视频移动.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/将子文件夹中的视频图片文件移动到根目录.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/小雅strm生成器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/按文件前缀名整理到子文件夹.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/文件搜索器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/生成strm.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/电报tdl下载器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/电报下载器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/电报带标记下载.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/监控文件夹.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/短视频预览播放.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/移动小视频文件.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/移除文件中的指定字符添加盘编号.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/网络视频整理.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/网络视频整理播放器1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/脚本快捷导航.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/自动执行批处理.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/记事本.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/back/重复文件查找器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/common_folders.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/config.ini" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/debug_page.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/default_folder.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/deletions.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/extract_links_simple.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/extracted_links.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/file_organizer_log.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/folder_structure_learning.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/genres_dict.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/genres_dict.json1" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/genres_dict.json2" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/html_link_extractor.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/image_settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/image_viewer.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/install_context_menu.reg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/javbus刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/javbus无码刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/javdb_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/javdb刮削 chrome.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/javdb刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/javdb有码刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/jukujo刮削.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/last_folder.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/last_position.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/delete_log.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250512.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250513.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250514.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250516.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250517.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250518.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250520.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250521.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250522.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250523.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250524.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250525.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250528.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250529.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250530.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250531.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250602.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250605.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250607.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250608.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250612.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250613.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250616.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250617.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250618.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250620.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250621.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250622.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250626.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250627.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250628.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250629.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250701.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250703.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250704.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250705.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250706.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250707.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250708.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250710.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250712.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250713.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250716.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250718.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250719.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250720.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250723.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250724.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250725.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250726.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250727.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250730.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250731.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/logs/file_indexer_20250801.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/messages.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/modification_log.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/move_history.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/move_history.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/nfo文件整理器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/notepad.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/path_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/porhub 视频播放.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/prefix_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/remove_duplicates.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/rename_video.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/replace_spaces_in_filename.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/replace_spaces_menu.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/replacements.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scraper.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/scripts.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/search_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/sleep_data.xlsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/strm生成器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/symlinks.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/telegram_reader.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/telegram_reader.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_delete_special_chars.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/test_mp3_rename.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/translate.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/uninstall_context_menu.reg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/user_preferences.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/video_data.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/viewer_config.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wangluo_index.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/webdav_logins.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/.idea/inspectionProfiles/profiles_settings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/.idea/代码.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/DB Browser for SQLite.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch10/.ipynb_checkpoints/10.3 案例1：运用海龟交易策略进行股票投资-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch10/10.3 案例1：运用海龟交易策略进行股票投资.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch10/data/0601857股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch10/data/0601857股票历史交易数据（清洗后）.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch11/.ipynb_checkpoints/Untitled-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch11/11.4 案例2：基于价差的高频交易策略实施过程.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch11/11.4 案例2：基于价差的高频交易策略实施过程/HighFrequencyTrading.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch11/11.4 案例2：基于价差的高频交易策略实施过程/trading.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch11/HighFrequencyTradingSystem.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/.ipynb_checkpoints/10.3 案例1：运用海龟交易策略进行股票投资-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/.ipynb_checkpoints/12.6 案例4：中国石油和中国石化配对交易套利-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/12.5 案例3：量化交易配对交易套利/airs_trading.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/12.6 案例4：中国石油和中国石化配对交易套利.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/data/0600028股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/data/0601600股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/data/0601857股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/data/中国石化.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch12/data/中国石油.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/.ipynb_checkpoints/13.3.3 案例1：使用分类策略预测苹果股票走势-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/.ipynb_checkpoints/13.3.4 案例2：使用回归策略预测苹果股票走势-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/13.3.3 案例1：使用分类策略预测苹果股票走势.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/13.3.4 案例2：使用回归策略预测苹果股票走势.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/data/AAPL.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/data/HistoricalData_1687681340565.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/data/中国石化.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/data/中国石油.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/data/中国铝业.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch13/model.pkl" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2-checkpoint.3 第一个Python程序" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.10 字符串类型-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.10.2 字符串格式化-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.11 函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.11.1 匿名函数与lambda表达式-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.11.2 数据处理中的两个常用函数1．过滤函数filter-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.11.2 数据处理中的两个常用函数2．映射函数map-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.12.1 文件操作-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.12.1 文件操作1．打开文件-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.12.2．关闭文件-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.13.1 捕获异常-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.13.2 释放资源1．finally代码块-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.13.2 释放资源2．with as代码块自动资源管理-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.14.1 创建线程-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.3 第一个Python程序-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.4-checkpoint.3 变量明" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.4.4 语句-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.4.5 Python代码块-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.4.6 模块-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.5.1 数据类型-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.5.2 运算符-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6 控制语句 1. if结构-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6 控制语句 2．if-else结构-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6 控制语句 3．elif结构-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6.2 循环语句1．while语句-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6.2 循环语句2．for语句-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6.3 跳转语句1．break语句-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.6.3 跳转语句2．continue语句-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.7.1 索引操作 -checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.7.2 序列切片-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.7.3 可变序列——列表-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.7.4 不可变序列——元组-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.7.5 列表推导式-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.8.1 创建集合-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.8.2 集合推导式-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.9.1 创建字典-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/.ipynb_checkpoints/2.9.2 字典推导式-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.10 字符串类型.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.10.2 字符串格式化.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.11 函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.11.1 匿名函数与lambda表达式.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.11.2 数据处理中的两个常用函数1．过滤函数filter.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.11.2 数据处理中的两个常用函数2．映射函数map.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.12.1 文件操作.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.12.1 文件操作1打开文件.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.12.1 文件操作2.关闭文件.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.12.1 文件操作3.文本文件读写.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.12.1 文件操作4.二进制文件读写.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.13.1 捕获异常.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.13.2 释放资源1．finally代码块.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.13.2 释放资源2．with as代码块自动资源管理.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.14.1 创建线程.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.3 第一个Python程序.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.4.4 语句.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.4.5 Python代码块.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.4.6 模块.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.5.1 数据类型.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.5.2 运算符.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6 控制语句 1. if结构.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6 控制语句 2．if-else结构.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6 控制语句 3．elif结构.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6.2 循环语句1．while语句.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6.2 循环语句2．for语句.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6.3 跳转语句1．break语句.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.6.3 跳转语句2．continue语句.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.7.1 索引操作 .ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.7.2 序列切片.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.7.3 可变序列——列表.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.7.4 不可变序列——元组.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.7.5 列表推导式.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.8.1 创建集合.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.8.2 集合推导式.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.9.1 创建字典.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/2.9.2 字典推导式.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/ch2.3.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/coco2dxcplus.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/copy.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/copy.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/copy2.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/data/date.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/hello.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/module1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/test.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch2/test1.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.2 创建Series对象1.使用列表创建Series-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.2 创建Series对象1使用NumPy数组创建Series-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.2 创建Series对象3.指定索引-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.2 创建Series对象4.使用标量创建Series-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.2 创建Series对象5.使用字典创建Series-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.4 通过下标访问Series数据1.通过标签下标访问Series数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.4 通过下标访问Series数据2. 通过位置下标访问Series数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.5 通过切片访问Series数据1.通过标签切片访问Series数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.10.5 通过切片访问Series数据2. 通过位置下标访问Series数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.11.1 创建DataFrame对象1. 使用列表创建DataFrame-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.11.1 创建DataFrame对象2.指定行标签和列标签-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.11.1 创建DataFrame对象3. 使用字典创建DataFrame对象-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.12.1 访问DataFrame列-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.12.2 访问DataFrame行-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.12.3 切片访问-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.13.2 案例1：从CSV文件读取货币供应量数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.13.4 案例2：写入水果数据到CSV文件-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.13.6 案例3：从Excel文件读取货币供应量数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.13.8 案例4：从数据库读取苹果股票数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.2.1 创建一维数组-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.2.2 指定数组数据类型-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.2.4 使用arange函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.2.5 等差数列与linspace函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.2.6 等比数列与logspace函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.3.1 创建二维数组1. 使用列表嵌套-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.3.1 创建二维数组2. 使用reshape()函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.4.1 使用ones函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.4.2 使用zeros函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.4.3 使用empty函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.4.4 使用full函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.4.5 使用identity-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.4.6 使用eye函数-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.5 数组的属性-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.7 三维数组-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.8.1 索引访问1. 一维数组索引访问-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.8.1 索引访问2. 二数组维索引访问-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.8.2 切片访问1. 一维数组切片访问-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.8.2 切片访问2. 二维数组切片访问-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/.ipynb_checkpoints/3.8.3 花式索引-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.2 创建Series对象1.使用列表创建Series.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.2 创建Series对象1使用NumPy数组创建Series.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.2 创建Series对象3.指定索引.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.2 创建Series对象4.使用标量创建Series.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.2 创建Series对象5.使用字典创建Series.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.4 通过下标访问Series数据1.通过标签下标访问Series数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.4 通过下标访问Series数据2. 通过位置下标访问Series数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.5 通过切片访问Series数据1.通过标签切片访问Series数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.10.5 通过切片访问Series数据2. 通过位置下标访问Series数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.11.1 创建DataFrame对象1. 使用列表创建DataFrame.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.11.1 创建DataFrame对象2.指定行标签和列标签.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.11.1 创建DataFrame对象3. 使用字典创建DataFrame对象.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.12.1 访问DataFrame列.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.12.2 访问DataFrame行.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.12.3 切片访问.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.13.2 案例1：从CSV文件读取货币供应量数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.13.4 案例2：写入水果数据到CSV文件.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.13.6 案例3：从Excel文件读取货币供应量数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.13.8 案例4：从数据库读取苹果股票数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.2.1 创建一维数组.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.2.2 指定数组数据类型.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.2.4 使用arange函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.2.5 等差数列与linspace函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.2.6 等比数列与logspace函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.3.1 创建二维数组1. 使用列表嵌套.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.3.1 创建二维数组2. 使用reshape()函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.4.1 使用ones函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.4.2 使用zeros函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.4.3 使用empty函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.4.4 使用full函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.4.5 使用identity.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.4.6 使用eye函数.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.5 数组的属性.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.7 三维数组.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.8.1 索引访问1. 一维数组索引访问.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.8.1 索引访问2. 二数组维索引访问.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.8.2 切片访问1. 一维数组切片访问.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.8.2 切片访问2. 二维数组切片访问.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/3.8.3 花式索引.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/NASDAQ.sql" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/NASDAQ_DB.db" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/水果.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/的商品房销售面月度数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/的商品房销售面月度数据.xls" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/货币供应量月度数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch3/data/货币供应量月度数据.xls" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.3 绘制折线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.4 绘制柱状图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.5 绘制饼状图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.6 绘制散点图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.7 案例1：贵州茅台股票历史成交量折线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.7 绘制子图表-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.2.8 案例2：绘制贵州茅台股票OHLC折线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.3.2 案例3：绘制贵州茅台股票K线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.4.1 箱形图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.4.2 小提琴图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.4.3 关联线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.4.4 Dist图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.4.5 线性回归图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/.ipynb_checkpoints/4.4.6 热力图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.3 绘制折线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.4 绘制柱状图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.5 绘制饼状图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.6 绘制散点图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.7 案例1：贵州茅台股票历史成交量折线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.7 绘制子图表.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.2.8 案例2：绘制贵州茅台股票OHLC折线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.3.2 案例3：绘制贵州茅台股票K线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.4.1 箱形图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.4.2 小提琴图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.4.3 关联线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.4.4 Dist图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.4.5 线性回归图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/4.4.6 热力图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/data/AAPL.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/data/stock_data.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/data/stock_data（4测试绘制线性回归图）.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/data/股票数据Test.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/data/贵州茅台股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch4/贵州茅台股票OHLC折线图.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.idea/workspace.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.2.2 案例1：爬取纳斯达克股票数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.3.2 案例2：解析纳斯达克股票数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.3.4 案例3：爬取搜狐证券贵州茅台股票数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.3.5 案例4：使用Selenium解析HTML数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.4.3 案例5：使用Tushare API获取贵州茅台股票数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.4.3 案例6：处理股票数据缺失值-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.4.4 案例7：处理股票数据类型不一致-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.4.5 案例8：处理股票数据异常值-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.3 案例7：处理股票数据缺失值-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.3 案例9：股票行业相关性分析-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.4 案例8：处理股票数据类型不一致-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.5 案例10：苹果股票数据统计描述和摘要分析-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.5 案例9：处理股票数据异常值-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.7 使用直方图统计分析数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.5.8 使用箱线图统计分析数据-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.6.3 案例10：股票行业相关性分析-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/.ipynb_checkpoints/5.6.5 案例11：苹果股票数据统计描述和摘要分析-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.2.2 案例1：爬取纳斯达克股票数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.3.2 案例2：解析纳斯达克股票数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.3.4 案例3：爬取搜狐证券贵州茅台股票数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.3.5 案例4：使用Selenium解析HTML数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.4.3 案例5：使用Tushare API获取贵州茅台股票数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.5.3 案例7：处理股票数据缺失值.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.5.4 案例8：处理股票数据类型不一致.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.5.5 案例9：处理股票数据异常值.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.6.3 案例10：股票行业相关性分析.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.6.5 案例11：苹果股票数据统计描述和摘要分析.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.6.7 使用直方图统计分析数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/5.6.8 使用箱线图统计分析数据.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/AAPL.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/lsjysj_600519.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/nasdaq-Apple1.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/stock_data - 副本.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/stock_data.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/stock_data（因子分析）.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/stock_data（相关性）.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/搜狐证券贵州茅台股票数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/纳斯达克股票数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/股票交易数据0.xlsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/股票数据Test.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/data/贵州茅台股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch5/geckodriver.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/.ipynb_checkpoints/8.3 案例1：使用ChatGPT辅助股票移动平均线策略分析-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/.ipynb_checkpoints/8.3.2 K线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/.ipynb_checkpoints/8.3.3 合并K线图和移动平均线图-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/.ipynb_checkpoints/8.3.4 初始策略规则-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/8.3 案例1：使用ChatGPT辅助股票移动平均线策略分析.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/8.3.2 K线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/8.3.3 合并K线图和移动平均线图.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/8.3.4 初始策略规则.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch8/data/AAPL.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch9/.ipynb_checkpoints/10.3 案例1：运用海龟交易策略进行股票投资-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch9/.ipynb_checkpoints/9.4 案例1：使用ChatGPT辅助贵州茅台股票价格和RSI交易信号分析-checkpoint.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch9/9.4 案例1：使用ChatGPT辅助贵州茅台股票价格和RSI交易信号分析.ipynb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/ch9/data/贵州茅台股票历史交易数据.csv" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/chromedriver_win32.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/geckodriver-v0.33.0-win32.zip" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/python-3.11.3-amd64.exe" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/代码/自动解压缩.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/分割文件夹.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/分类移动pornhub视频.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/判断文件夹中是否有视频文件及nfo.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/卸载右键菜单.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/另一个_测试_文件.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/启动提取器.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/启动文件整理工具.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/商片视频播放.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/图片预览查看管理器-简版.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/图片预览查看管理器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/安装右键菜单.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/将子文件夹都移动到根目录.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/将视频图片汇总并选择按文件夹名改名.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/快速卸载右键菜单.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/快速安装右键菜单.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/截图.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/把文件向上层移动.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/按女优名整理网络视频.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/按影片类型演员建立软连接.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/按番号搜索重名文件并删除.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/收藏片汇总整理.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/整理商片文件夹.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文件搜索.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/文本文件编码转换器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/新建文件夹功能说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/油猴本地服务器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/测试 安装 脚本.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/测试_文件_名称.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/测试安装.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/清理低分辨图片和视频及杂项文件.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/演示功能.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/演示和测试.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/电报下载器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/电报文件按前缀汇总文件夹.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/电报文件整理.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/电报链接提取器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/睡眠记录.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/短视频预览播放.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/移动冲突处理改进说明.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/移动文件名中有关键字的视频文件.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/移动无码商片.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/移动没有genre的影片文件夹.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/移除文件中的字符并记录.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/简单卸载.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/简单安装.bat" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/网络媒体文件夹整理.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/网络视频整理播放器.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/网络视频整理播放器1.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/翻译文件夹名.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/脚本快捷导航.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/获取javdb图片.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/规范文件及文件夹名.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/规范文件夹名.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/规范百度网盘下载的文件名.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/视频预览分类.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/解压缩.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/解压缩GUI.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/长文件名重命名.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/项目说明.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/javbus刮削.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/javbus无码刮削.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/删除小图片.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/判断文件夹中是否有视频文件及nfo.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/商片视频播放.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/截图.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/油猴本地服务器.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/移除文件中的字符并记录.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/自动解压缩.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/规范百度网盘下载的文件名.py" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://Z:/py/javdb刮削多盘.py" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2c2VtM40rt4g0oQb6jKhjj7Dr8v" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;Python.1.executor&quot;: &quot;Run&quot;,
    &quot;Python.11.executor&quot;: &quot;Run&quot;,
    &quot;Python.12.executor&quot;: &quot;Run&quot;,
    &quot;Python.13.executor&quot;: &quot;Run&quot;,
    &quot;Python.14.executor&quot;: &quot;Run&quot;,
    &quot;Python.2.executor&quot;: &quot;Run&quot;,
    &quot;Python.3.executor&quot;: &quot;Run&quot;,
    &quot;Python.4.executor&quot;: &quot;Run&quot;,
    &quot;Python.5.executor&quot;: &quot;Run&quot;,
    &quot;Python.6.executor&quot;: &quot;Run&quot;,
    &quot;Python.auto_copy.executor&quot;: &quot;Run&quot;,
    &quot;Python.find_same_size_files.executor&quot;: &quot;Run&quot;,
    &quot;Python.image_handle-1.executor&quot;: &quot;Run&quot;,
    &quot;Python.image_viewer.executor&quot;: &quot;Run&quot;,
    &quot;Python.javbus刮削.executor&quot;: &quot;Run&quot;,
    &quot;Python.javbus无码-1.executor&quot;: &quot;Run&quot;,
    &quot;Python.javbus无码刮削.executor&quot;: &quot;Run&quot;,
    &quot;Python.javdb刮削.executor&quot;: &quot;Run&quot;,
    &quot;Python.javdb刮削多盘.executor&quot;: &quot;Run&quot;,
    &quot;Python.javdb无码刮削.executor&quot;: &quot;Run&quot;,
    &quot;Python.javdb有码刮削.executor&quot;: &quot;Run&quot;,
    &quot;Python.jukujo刮削.executor&quot;: &quot;Run&quot;,
    &quot;Python.nfo文件整理器.executor&quot;: &quot;Run&quot;,
    &quot;Python.porhub 视频播放.executor&quot;: &quot;Run&quot;,
    &quot;Python.pyton本地服务器.executor&quot;: &quot;Run&quot;,
    &quot;Python.rename_video.executor&quot;: &quot;Run&quot;,
    &quot;Python.strm生成器.executor&quot;: &quot;Run&quot;,
    &quot;Python.symlinks.executor&quot;: &quot;Run&quot;,
    &quot;Python.tdl前端.executor&quot;: &quot;Run&quot;,
    &quot;Python.tdl配套程序.executor&quot;: &quot;Run&quot;,
    &quot;Python.删除小图片.executor&quot;: &quot;Run&quot;,
    &quot;Python.删除扩展名中的汉字.executor&quot;: &quot;Run&quot;,
    &quot;Python.合并wmv.executor&quot;: &quot;Run&quot;,
    &quot;Python.商片视频播放.executor&quot;: &quot;Run&quot;,
    &quot;Python.图片爬取1.executor&quot;: &quot;Run&quot;,
    &quot;Python.图片爬取2.executor&quot;: &quot;Run&quot;,
    &quot;Python.图片预览查看管理器.executor&quot;: &quot;Run&quot;,
    &quot;Python.多重子文件夹的视频移动.executor&quot;: &quot;Run&quot;,
    &quot;Python.将子文件夹中的视频图片文件移动到根目录.executor&quot;: &quot;Run&quot;,
    &quot;Python.将子文件夹中的视频文件移动到根目录.executor&quot;: &quot;Run&quot;,
    &quot;Python.将子文件夹都移动到根目录.executor&quot;: &quot;Run&quot;,
    &quot;Python.将视频图片汇总并选择按文件夹名改名.executor&quot;: &quot;Run&quot;,
    &quot;Python.小雅strm生成器.executor&quot;: &quot;Run&quot;,
    &quot;Python.截图.executor&quot;: &quot;Run&quot;,
    &quot;Python.按影片类型演员建立软连接.executor&quot;: &quot;Run&quot;,
    &quot;Python.按文件前缀名整理到子文件夹.executor&quot;: &quot;Run&quot;,
    &quot;Python.整理商片文件夹.executor&quot;: &quot;Run&quot;,
    &quot;Python.整理图片文件夹.executor&quot;: &quot;Run&quot;,
    &quot;Python.整理文件夹 将名字中的指定字符移除.executor&quot;: &quot;Run&quot;,
    &quot;Python.文件搜索.executor&quot;: &quot;Run&quot;,
    &quot;Python.文件搜索器.executor&quot;: &quot;Run&quot;,
    &quot;Python.文本文件编码转换器.executor&quot;: &quot;Run&quot;,
    &quot;Python.油猴本地服务器.executor&quot;: &quot;Run&quot;,
    &quot;Python.清理低分辨图片和视频及杂项文件.executor&quot;: &quot;Run&quot;,
    &quot;Python.生成strm.executor&quot;: &quot;Run&quot;,
    &quot;Python.电报tdl下载器.executor&quot;: &quot;Run&quot;,
    &quot;Python.电报下载器.executor&quot;: &quot;Run&quot;,
    &quot;Python.电报带标记下载.executor&quot;: &quot;Run&quot;,
    &quot;Python.电报批量下载器.executor&quot;: &quot;Run&quot;,
    &quot;Python.电报文件按前缀汇总文件夹.executor&quot;: &quot;Run&quot;,
    &quot;Python.电报链接提取器.executor&quot;: &quot;Run&quot;,
    &quot;Python.百度图片搜索.executor&quot;: &quot;Run&quot;,
    &quot;Python.监控文件夹.executor&quot;: &quot;Run&quot;,
    &quot;Python.睡眠记录.executor&quot;: &quot;Run&quot;,
    &quot;Python.短视频预览播放.executor&quot;: &quot;Run&quot;,
    &quot;Python.移动小视频文件.executor&quot;: &quot;Run&quot;,
    &quot;Python.移动无码商片.executor&quot;: &quot;Run&quot;,
    &quot;Python.移除文件中的字符并记录.executor&quot;: &quot;Run&quot;,
    &quot;Python.移除文件中的指定字符添加盘编号.executor&quot;: &quot;Run&quot;,
    &quot;Python.网络媒体文件夹整理.executor&quot;: &quot;Run&quot;,
    &quot;Python.网络视频整理.executor&quot;: &quot;Run&quot;,
    &quot;Python.网络视频整理播放器.executor&quot;: &quot;Run&quot;,
    &quot;Python.网络视频整理播放器1.executor&quot;: &quot;Run&quot;,
    &quot;Python.脚本快捷导航.executor&quot;: &quot;Run&quot;,
    &quot;Python.自动执行批处理.executor&quot;: &quot;Run&quot;,
    &quot;Python.自动解压缩.executor&quot;: &quot;Run&quot;,
    &quot;Python.获取javdb图片.executor&quot;: &quot;Run&quot;,
    &quot;Python.规范文件及文件夹名.executor&quot;: &quot;Run&quot;,
    &quot;Python.规范百度网盘下载的文件名.executor&quot;: &quot;Run&quot;,
    &quot;Python.视频播放.executor&quot;: &quot;Run&quot;,
    &quot;Python.视频预览分类.executor&quot;: &quot;Run&quot;,
    &quot;Python.解压缩.executor&quot;: &quot;Run&quot;,
    &quot;Python.记事本.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/work/股票/回测与交易&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;inlay.hints&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-7a29c1521ef0-c986f194a52a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-233.11799.298" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="059519b0-c8ec-4cb9-8853-df06566cad9a" name="更改" comment="" />
      <created>1707309874023</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1707309874023</updated>
      <workItem from="1707309880451" duration="3162000" />
      <workItem from="1707381533617" duration="6000" />
      <workItem from="1707559859664" duration="1325000" />
      <workItem from="1707640351582" duration="735000" />
      <workItem from="1707720981982" duration="819000" />
      <workItem from="1707741860533" duration="1612000" />
      <workItem from="1707783965230" duration="603000" />
      <workItem from="1707900547443" duration="1221000" />
      <workItem from="1707957124516" duration="7492000" />
      <workItem from="1708046038861" duration="2628000" />
      <workItem from="1708161250966" duration="366000" />
      <workItem from="1708251778524" duration="11262000" />
      <workItem from="1708311168201" duration="8443000" />
      <workItem from="1708424619353" duration="3956000" />
      <workItem from="1708510346950" duration="1205000" />
      <workItem from="1708596473158" duration="1008000" />
      <workItem from="1708607576120" duration="94000" />
      <workItem from="1708611028668" duration="942000" />
      <workItem from="1708681209157" duration="9479000" />
      <workItem from="1708697370779" duration="2087000" />
      <workItem from="1708734459465" duration="20365000" />
      <workItem from="1708821858737" duration="32741000" />
      <workItem from="1708942067021" duration="6959000" />
      <workItem from="1709026552098" duration="2457000" />
      <workItem from="1709035226453" duration="4190000" />
      <workItem from="1709115072251" duration="13748000" />
      <workItem from="1709199256637" duration="9489000" />
      <workItem from="1709286089012" duration="822000" />
      <workItem from="1709288781790" duration="6350000" />
      <workItem from="1709342310737" duration="1244000" />
      <workItem from="1709444353082" duration="10356000" />
      <workItem from="1709633565020" duration="3443000" />
      <workItem from="1709725350951" duration="801000" />
      <workItem from="1709726461140" duration="612000" />
      <workItem from="1709812224893" duration="735000" />
      <workItem from="1709957077700" duration="1168000" />
      <workItem from="1710063878415" duration="3374000" />
      <workItem from="1710124467424" duration="5910000" />
      <workItem from="1710203808192" duration="3657000" />
      <workItem from="1710306451262" duration="1206000" />
      <workItem from="1710321464358" duration="603000" />
      <workItem from="1710383560654" duration="104000" />
      <workItem from="1710413282410" duration="1217000" />
      <workItem from="1710463020024" duration="7000" />
      <workItem from="1710590920595" duration="602000" />
      <workItem from="1710669517646" duration="2772000" />
      <workItem from="1710757262702" duration="2120000" />
      <workItem from="1710817898214" duration="2555000" />
      <workItem from="1710821562920" duration="12613000" />
      <workItem from="1710901798371" duration="8972000" />
      <workItem from="1711000776776" duration="4000" />
      <workItem from="1711016901285" duration="1261000" />
      <workItem from="1711102198154" duration="1545000" />
      <workItem from="1711158893210" duration="1468000" />
      <workItem from="1711200922925" duration="12000" />
      <workItem from="1711270899998" duration="1226000" />
      <workItem from="1711757285029" duration="4431000" />
      <workItem from="1711790789547" duration="915000" />
      <workItem from="1711849199299" duration="1299000" />
      <workItem from="1712183534910" duration="98000" />
      <workItem from="1712183644545" duration="5540000" />
      <workItem from="1712275149041" duration="2035000" />
      <workItem from="1712356864502" duration="6354000" />
      <workItem from="1712395086123" duration="83000" />
      <workItem from="1712395349130" duration="1539000" />
      <workItem from="1712726272109" duration="2902000" />
      <workItem from="1712729964722" duration="3547000" />
      <workItem from="1712900262253" duration="4887000" />
      <workItem from="1712961131989" duration="1137000" />
      <workItem from="1713080242184" duration="1509000" />
      <workItem from="1713157458113" duration="1074000" />
      <workItem from="1713507030976" duration="3973000" />
      <workItem from="1713568854270" duration="4435000" />
      <workItem from="1713677522101" duration="2438000" />
      <workItem from="1713687187081" duration="16000" />
      <workItem from="1713687505463" duration="3344000" />
      <workItem from="1713755733095" duration="7444000" />
      <workItem from="1713835418192" duration="17852000" />
      <workItem from="1713932699729" duration="4614000" />
      <workItem from="1714014239785" duration="12898000" />
      <workItem from="1714092615067" duration="12758000" />
      <workItem from="1714126426021" duration="623000" />
      <workItem from="1714188168774" duration="3872000" />
      <workItem from="1714280484814" duration="7172000" />
      <workItem from="1714361656250" duration="8303000" />
      <workItem from="1714437684003" duration="20618000" />
      <workItem from="1714530116902" duration="1835000" />
      <workItem from="1714694109397" duration="1294000" />
      <workItem from="1715306977920" duration="2399000" />
      <workItem from="1715385512563" duration="9968000" />
      <workItem from="1715473098040" duration="10109000" />
      <workItem from="1715563554064" duration="9593000" />
      <workItem from="1715645985739" duration="25155000" />
      <workItem from="1715908554704" duration="3908000" />
      <workItem from="1715927949464" duration="6921000" />
      <workItem from="1715996050018" duration="1236000" />
      <workItem from="1716101929492" duration="838000" />
      <workItem from="1716174362977" duration="4715000" />
      <workItem from="1716253914617" duration="10242000" />
      <workItem from="1716287135373" duration="6062000" />
      <workItem from="1716337265907" duration="13823000" />
      <workItem from="1716424741108" duration="4332000" />
      <workItem from="1716511562136" duration="10136000" />
      <workItem from="1716713317554" duration="957000" />
      <workItem from="1716775164939" duration="9367000" />
      <workItem from="1716892620688" duration="612000" />
      <workItem from="1716986549186" duration="1204000" />
      <workItem from="1717030906081" duration="7919000" />
      <workItem from="1717130840898" duration="842000" />
      <workItem from="1717154343941" duration="1201000" />
      <workItem from="1717200699479" duration="2404000" />
      <workItem from="1717310475146" duration="2401000" />
      <workItem from="1717375035385" duration="10301000" />
      <workItem from="1717475408408" duration="4839000" />
      <workItem from="1717561510421" duration="15190000" />
      <workItem from="1717636993114" duration="8454000" />
      <workItem from="1717720475840" duration="10241000" />
      <workItem from="1717807803123" duration="1740000" />
      <workItem from="1717974858215" duration="7507000" />
      <workItem from="1718066846447" duration="21604000" />
      <workItem from="1718158721809" duration="3730000" />
      <workItem from="1718247522238" duration="3623000" />
      <workItem from="1718325353964" duration="20532000" />
      <workItem from="1718409248207" duration="2878000" />
      <workItem from="1718494236723" duration="1880000" />
      <workItem from="1718584540264" duration="11341000" />
      <workItem from="1718669753927" duration="27140000" />
      <workItem from="1718764208850" duration="11024000" />
      <workItem from="1718850552844" duration="12429000" />
      <workItem from="1718933041817" duration="6642000" />
      <workItem from="1719005861875" duration="811000" />
      <workItem from="1719126298388" duration="2857000" />
      <workItem from="1719196287331" duration="5521000" />
      <workItem from="1719277351738" duration="1050000" />
      <workItem from="1719400211778" duration="616000" />
      <workItem from="1719448535865" duration="1651000" />
      <workItem from="1719462084548" duration="937000" />
      <workItem from="1719483131746" duration="1205000" />
      <workItem from="1719525768602" duration="310000" />
      <workItem from="1719542450274" duration="1725000" />
      <workItem from="1719619619310" duration="2940000" />
      <workItem from="1719649637879" duration="616000" />
      <workItem from="1719695453486" duration="4436000" />
      <workItem from="1719915562889" duration="1198000" />
      <workItem from="1719956383484" duration="5114000" />
      <workItem from="1720058202902" duration="1889000" />
      <workItem from="1720128148612" duration="1377000" />
      <workItem from="1720337054633" duration="2768000" />
      <workItem from="1720400820312" duration="6532000" />
      <workItem from="1720499652000" duration="2678000" />
      <workItem from="1720581686425" duration="2405000" />
      <workItem from="1720668614824" duration="1572000" />
      <workItem from="1720748108920" duration="2862000" />
      <workItem from="1720818651217" duration="5008000" />
      <workItem from="1720941342264" duration="1799000" />
      <workItem from="1721012243190" duration="1715000" />
      <workItem from="1721017912415" duration="1193000" />
      <workItem from="1721079890109" duration="1433000" />
      <workItem from="1721089620244" duration="1203000" />
      <workItem from="1721174723767" duration="4951000" />
      <workItem from="1721253107842" duration="1071000" />
      <workItem from="1721273214302" duration="3617000" />
      <workItem from="1721357138284" duration="6634000" />
      <workItem from="1721426920462" duration="2813000" />
      <workItem from="1721466354143" duration="3252000" />
      <workItem from="1721510995474" duration="2885000" />
      <workItem from="1721607141795" duration="7854000" />
      <workItem from="1721711149767" duration="4732000" />
      <workItem from="1721766964874" duration="1774000" />
      <workItem from="1721858291407" duration="4589000" />
      <workItem from="1721956003837" duration="7405000" />
      <workItem from="1721966283591" duration="2584000" />
      <workItem from="1721972095567" duration="6625000" />
      <workItem from="1722029946578" duration="3946000" />
      <workItem from="1722148568948" duration="1208000" />
      <workItem from="1722225588188" duration="1217000" />
      <workItem from="1722310019109" duration="6820000" />
      <workItem from="1722386810027" duration="5825000" />
      <workItem from="1722394807752" duration="478000" />
      <workItem from="1722395449549" duration="3443000" />
      <workItem from="1722472398668" duration="3903000" />
      <workItem from="1722558044290" duration="6156000" />
      <workItem from="1722633604991" duration="613000" />
      <workItem from="1722743845690" duration="2654000" />
      <workItem from="1722819145553" duration="1738000" />
      <workItem from="1722892465173" duration="2475000" />
      <workItem from="1722979495896" duration="10462000" />
      <workItem from="1723064518058" duration="9230000" />
      <workItem from="1723166054180" duration="13865000" />
      <workItem from="1723238125082" duration="2426000" />
      <workItem from="1723368112618" duration="603000" />
      <workItem from="1723410007904" duration="7117000" />
      <workItem from="1723498002068" duration="2290000" />
      <workItem from="1723546483767" duration="1753000" />
      <workItem from="1723611117568" duration="1204000" />
      <workItem from="1723669905877" duration="5596000" />
      <workItem from="1723770454997" duration="4599000" />
      <workItem from="1723803992412" duration="724000" />
      <workItem from="1723842040503" duration="2303000" />
      <workItem from="1723964690032" duration="1324000" />
      <workItem from="1724016351179" duration="4725000" />
      <workItem from="1724063819694" duration="665000" />
      <workItem from="1724101825430" duration="2923000" />
      <workItem from="1724150660202" duration="551000" />
      <workItem from="1724187722647" duration="4676000" />
      <workItem from="1724274050209" duration="2820000" />
      <workItem from="1724362605313" duration="655000" />
      <workItem from="1724446715604" duration="1986000" />
      <workItem from="1724574655582" duration="1143000" />
      <workItem from="1724668408979" duration="601000" />
      <workItem from="1724721885523" duration="3679000" />
      <workItem from="1724817532342" duration="3812000" />
      <workItem from="1724839555912" duration="940000" />
      <workItem from="1724903978942" duration="777000" />
      <workItem from="1725021967216" duration="601000" />
      <workItem from="1725057598320" duration="1212000" />
      <workItem from="1725181995890" duration="613000" />
      <workItem from="1725249708737" duration="1218000" />
      <workItem from="1725339251138" duration="602000" />
      <workItem from="1725449641919" duration="2113000" />
      <workItem from="1725490830745" duration="70000" />
      <workItem from="1725532104686" duration="1679000" />
      <workItem from="1725595530971" duration="605000" />
      <workItem from="1725660889497" duration="590000" />
      <workItem from="1725783080717" duration="2023000" />
      <workItem from="1725846901853" duration="4397000" />
      <workItem from="1725930494871" duration="12407000" />
      <workItem from="1726029928921" duration="6200000" />
      <workItem from="1726113835139" duration="1640000" />
      <workItem from="1726222158854" duration="1720000" />
      <workItem from="1726225371878" duration="1218000" />
      <workItem from="1726309698905" duration="718000" />
      <workItem from="1726356620377" duration="3058000" />
      <workItem from="1726448791456" duration="486000" />
      <workItem from="1726541157067" duration="978000" />
      <workItem from="1726632424256" duration="1930000" />
      <workItem from="1726712894469" duration="12957000" />
      <workItem from="1726796337367" duration="3839000" />
      <workItem from="1726899341190" duration="2401000" />
      <workItem from="1726956806132" duration="2144000" />
      <workItem from="1727063735485" duration="2606000" />
      <workItem from="1727086119401" duration="598000" />
      <workItem from="1727166639436" duration="1716000" />
      <workItem from="1727261156407" duration="1512000" />
      <workItem from="1727262828818" duration="557000" />
      <workItem from="1727343472096" duration="830000" />
      <workItem from="1727433122097" duration="1198000" />
      <workItem from="1727484335890" duration="3246000" />
      <workItem from="1727578583252" duration="3460000" />
      <workItem from="1727658645410" duration="4053000" />
      <workItem from="1727740677333" duration="2702000" />
      <workItem from="1727827167558" duration="1805000" />
      <workItem from="1728512617178" duration="2061000" />
      <workItem from="1728609574423" duration="10876000" />
      <workItem from="1728697563543" duration="3463000" />
      <workItem from="1728724395551" duration="18000" />
      <workItem from="1728734057488" duration="1108000" />
      <workItem from="1728770749197" duration="3114000" />
      <workItem from="1728987121594" duration="665000" />
      <workItem from="1729163045207" duration="1868000" />
      <workItem from="1729246024113" duration="1198000" />
      <workItem from="1729291504549" duration="1198000" />
      <workItem from="1729407536394" duration="1051000" />
      <workItem from="1729472354896" duration="6279000" />
      <workItem from="1729592582371" duration="1200000" />
      <workItem from="1729679744033" duration="1219000" />
      <workItem from="1729717841027" duration="3181000" />
      <workItem from="1729850667691" duration="1807000" />
      <workItem from="1729890201524" duration="1907000" />
      <workItem from="1730013143614" duration="609000" />
      <workItem from="1730077328578" duration="7533000" />
      <workItem from="1730150505419" duration="6953000" />
      <workItem from="1730260557401" duration="6539000" />
      <workItem from="1730325006877" duration="883000" />
      <workItem from="1730347069160" duration="5968000" />
      <workItem from="1730435863076" duration="1256000" />
      <workItem from="1730496835492" duration="1805000" />
      <workItem from="1730620637621" duration="1971000" />
      <workItem from="1730670125786" duration="1560000" />
      <workItem from="1730682679875" duration="17899000" />
      <workItem from="1730755918176" duration="1273000" />
      <workItem from="1730803248354" duration="1033000" />
      <workItem from="1730841945019" duration="9703000" />
      <workItem from="1730928126107" duration="3657000" />
      <workItem from="1731014906037" duration="4468000" />
      <workItem from="1731104702439" duration="1201000" />
      <workItem from="1731796009865" duration="4132000" />
      <workItem from="1731891700749" duration="3418000" />
      <workItem from="1731989954213" duration="1812000" />
      <workItem from="1732100053758" duration="942000" />
      <workItem from="1732166081378" duration="643000" />
      <workItem from="1732326110009" duration="1841000" />
      <workItem from="1732431229545" duration="2579000" />
      <workItem from="1732505645194" duration="9397000" />
      <workItem from="1732570334606" duration="1593000" />
      <workItem from="1732581220924" duration="1197000" />
      <workItem from="1732702593777" duration="608000" />
      <workItem from="1732770436875" duration="145000" />
      <workItem from="1732789596294" duration="1775000" />
      <workItem from="1732829151651" duration="2160000" />
      <workItem from="1732930886128" duration="1811000" />
      <workItem from="1732965904710" duration="1028000" />
      <workItem from="1733004332790" duration="2728000" />
      <workItem from="1733044704295" duration="6162000" />
      <workItem from="1733100766770" duration="18124000" />
      <workItem from="1733174837925" duration="2461000" />
      <workItem from="1733221193231" duration="2652000" />
      <workItem from="1733288637181" duration="2128000" />
      <workItem from="1733347872633" duration="10295000" />
      <workItem from="1733435656926" duration="9472000" />
      <workItem from="1733529830935" duration="1062000" />
      <workItem from="1733651555875" duration="1305000" />
      <workItem from="1733704218287" duration="6188000" />
      <workItem from="1733781292689" duration="3595000" />
      <workItem from="1733913212759" duration="1704000" />
      <workItem from="1734084255853" duration="3700000" />
      <workItem from="1734131877554" duration="3156000" />
      <workItem from="1734245932390" duration="2236000" />
      <workItem from="1734309634863" duration="5769000" />
      <workItem from="1734384498941" duration="1426000" />
      <workItem from="1734395679315" duration="5172000" />
      <workItem from="1734412550988" duration="3220000" />
      <workItem from="1734471059640" duration="2881000" />
      <workItem from="1734488603830" duration="7757000" />
      <workItem from="1734557387501" duration="20913000" />
      <workItem from="1734643688775" duration="10473000" />
      <workItem from="1734745651736" duration="199000" />
      <workItem from="1734745857225" duration="1894000" />
      <workItem from="1734853362687" duration="1671000" />
      <workItem from="1734920031293" duration="2463000" />
      <workItem from="1734928951572" duration="4104000" />
      <workItem from="1735013336565" duration="2440000" />
      <workItem from="1735076045963" duration="1429000" />
      <workItem from="1735126138625" duration="607000" />
      <workItem from="1735162061759" duration="12789000" />
      <workItem from="1735248499684" duration="25540000" />
      <workItem from="1735339421131" duration="3811000" />
      <workItem from="1735423306988" duration="2275000" />
      <workItem from="1735528339035" duration="7532000" />
      <workItem from="1735604265328" duration="8905000" />
      <workItem from="1735683422192" duration="2679000" />
      <workItem from="1735713467571" duration="2282000" />
      <workItem from="1735779909595" duration="7517000" />
      <workItem from="1735855698459" duration="1545000" />
      <workItem from="1735877039232" duration="1752000" />
      <workItem from="1735939199463" duration="601000" />
      <workItem from="1736059400955" duration="2983000" />
      <workItem from="1736133590056" duration="5098000" />
      <workItem from="1736200053186" duration="3049000" />
      <workItem from="1736284614750" duration="1663000" />
      <workItem from="1736330697862" duration="1197000" />
      <workItem from="1736375608763" duration="2308000" />
      <workItem from="1736461014331" duration="1721000" />
      <workItem from="1736481667071" duration="1301000" />
      <workItem from="1736545237788" duration="2594000" />
      <workItem from="1736668275823" duration="1666000" />
      <workItem from="1736717691751" duration="6059000" />
      <workItem from="1736802496629" duration="6686000" />
      <workItem from="1736980340689" duration="2930000" />
      <workItem from="1737024081714" duration="1219000" />
      <workItem from="1737063823203" duration="432000" />
      <workItem from="1737109133250" duration="1516000" />
      <workItem from="1737238292516" duration="2360000" />
      <workItem from="1737411945994" duration="1373000" />
      <workItem from="1737456713799" duration="2259000" />
      <workItem from="1737497653006" duration="1769000" />
      <workItem from="1737537754335" duration="1336000" />
      <workItem from="1737580339413" duration="1799000" />
      <workItem from="1737627697315" duration="922000" />
      <workItem from="1737713242251" duration="1933000" />
      <workItem from="1737754248060" duration="4634000" />
      <workItem from="1737885009298" duration="1308000" />
      <workItem from="1737971762998" duration="122000" />
      <workItem from="1737972823834" duration="1155000" />
      <workItem from="1737979792766" duration="624000" />
      <workItem from="1738023519038" duration="277000" />
      <workItem from="1738028828192" duration="4575000" />
      <workItem from="1738112922467" duration="1920000" />
      <workItem from="1738135143038" duration="2400000" />
      <workItem from="1738214273230" duration="1968000" />
      <workItem from="1738372781407" duration="1807000" />
      <workItem from="1738451467265" duration="11583000" />
      <workItem from="1738536101189" duration="3841000" />
      <workItem from="1738620902910" duration="7822000" />
      <workItem from="1738721743968" duration="5348000" />
      <workItem from="1738793580941" duration="2654000" />
      <workItem from="1738889735584" duration="2473000" />
      <workItem from="1738964234701" duration="5595000" />
      <workItem from="1739050454440" duration="2410000" />
      <workItem from="1739137999821" duration="4670000" />
      <workItem from="1739265706216" duration="773000" />
      <workItem from="1739359750070" duration="647000" />
      <workItem from="1739440032781" duration="1673000" />
      <workItem from="1739483170413" duration="6611000" />
      <workItem from="1739570418193" duration="2613000" />
      <workItem from="1739689626939" duration="3943000" />
      <workItem from="1739786774371" duration="824000" />
      <workItem from="1739844426385" duration="2437000" />
      <workItem from="1739879656949" duration="321000" />
      <workItem from="1739948798189" duration="4252000" />
      <workItem from="1740002256392" duration="3101000" />
      <workItem from="1740114837622" duration="4073000" />
      <workItem from="1740178617585" duration="1906000" />
      <workItem from="1740285850624" duration="3281000" />
      <workItem from="1740360317973" duration="687000" />
      <workItem from="1740392253660" duration="2854000" />
      <workItem from="1740569898222" duration="1369000" />
      <workItem from="1740607169263" duration="1344000" />
      <workItem from="1740649403857" duration="1844000" />
      <workItem from="1740693297950" duration="2188000" />
      <workItem from="1740738830550" duration="398000" />
      <workItem from="1740740487398" duration="724000" />
      <workItem from="1740779171332" duration="1621000" />
      <workItem from="1740900881781" duration="3365000" />
      <workItem from="1740981307918" duration="606000" />
      <workItem from="1741070662953" duration="606000" />
      <workItem from="1741171137031" duration="1955000" />
      <workItem from="1741214383830" duration="633000" />
      <workItem from="1741252928022" duration="1771000" />
      <workItem from="1741298256506" duration="598000" />
      <workItem from="1741339962091" duration="1607000" />
      <workItem from="1741384766483" duration="2964000" />
      <workItem from="1741475454629" duration="3487000" />
      <workItem from="1741642565289" duration="2184000" />
      <workItem from="1741752906030" duration="2283000" />
      <workItem from="1741836857009" duration="4092000" />
      <workItem from="1742020578362" duration="1807000" />
      <workItem from="1742105766956" duration="2423000" />
      <workItem from="1742199767266" duration="76000" />
      <workItem from="1742293051931" duration="1095000" />
      <workItem from="1742377533016" duration="3260000" />
      <workItem from="1742420144698" duration="1964000" />
      <workItem from="1742466672861" duration="2727000" />
      <workItem from="1742535858179" duration="1806000" />
      <workItem from="1742594809717" duration="1641000" />
      <workItem from="1742680634386" duration="4268000" />
      <workItem from="1742767696742" duration="1206000" />
      <workItem from="1742853261559" duration="4259000" />
      <workItem from="1742941886430" duration="2244000" />
      <workItem from="1743024811703" duration="5298000" />
      <workItem from="1743203039853" duration="611000" />
      <workItem from="1743238041655" duration="3605000" />
      <workItem from="1743284945891" duration="4547000" />
      <workItem from="1743504002821" duration="1644000" />
      <workItem from="1743547311897" duration="1807000" />
      <workItem from="1743589473689" duration="1335000" />
      <workItem from="1743598700587" duration="5000" />
      <workItem from="1743656530610" duration="1784000" />
      <workItem from="1743681260512" duration="883000" />
      <workItem from="1743723117656" duration="3203000" />
      <workItem from="1743817081713" duration="800000" />
      <workItem from="1743922426965" duration="4161000" />
      <workItem from="1743991922752" duration="1534000" />
      <workItem from="1744073194108" duration="598000" />
      <workItem from="1744076639261" duration="1811000" />
      <workItem from="1744251142061" duration="1812000" />
      <workItem from="1744369140086" duration="327000" />
      <workItem from="1744410061286" duration="3308000" />
      <workItem from="1744529051280" duration="1799000" />
      <workItem from="1744707165061" duration="658000" />
      <workItem from="1744792647531" duration="1445000" />
      <workItem from="1744803972639" duration="1348000" />
      <workItem from="1744888749995" duration="1140000" />
      <workItem from="1744962877697" duration="1690000" />
      <workItem from="1745025693766" duration="1887000" />
      <workItem from="1745137642908" duration="599000" />
      <workItem from="1745230668054" duration="831000" />
      <workItem from="1745378203327" duration="4240000" />
      <workItem from="1745576304904" duration="1232000" />
      <workItem from="1745619664130" duration="4178000" />
      <workItem from="1745718926334" duration="3094000" />
      <workItem from="1745806704995" duration="1777000" />
      <workItem from="1745890359720" duration="1319000" />
      <workItem from="1746002208270" duration="1747000" />
      <workItem from="1746056968494" duration="2394000" />
      <workItem from="1746157700660" duration="1391000" />
      <workItem from="1746254862377" duration="1478000" />
      <workItem from="1746433051104" duration="1461000" />
      <workItem from="1746440205163" duration="1339000" />
      <workItem from="1746494458927" duration="1235000" />
      <workItem from="1746576783118" duration="1631000" />
      <workItem from="1746779311551" duration="2044000" />
      <workItem from="1746835702879" duration="7887000" />
      <workItem from="1746923867643" duration="3569000" />
      <workItem from="1747019944107" duration="4275000" />
      <workItem from="1747098024301" duration="4932000" />
      <workItem from="1747217826009" duration="1764000" />
      <workItem from="1747270304759" duration="5198000" />
      <workItem from="1747353603927" duration="2906000" />
      <workItem from="1747432206054" duration="1451000" />
      <workItem from="1747549260362" duration="2759000" />
      <workItem from="1747703272732" duration="2476000" />
      <workItem from="1747827161965" duration="638000" />
      <workItem from="1747898924244" duration="1009000" />
      <workItem from="1747913356641" duration="1290000" />
      <workItem from="1747966225144" duration="4321000" />
      <workItem from="1748037780297" duration="2043000" />
      <workItem from="1748160407197" duration="2390000" />
      <workItem from="1748241270797" duration="615000" />
      <workItem from="1748419963827" duration="2528000" />
      <workItem from="1748515053591" duration="1886000" />
      <workItem from="1748601946495" duration="658000" />
      <workItem from="1748660534318" duration="2011000" />
      <workItem from="1748742681721" duration="741000" />
      <workItem from="1748816827121" duration="3327000" />
      <workItem from="1748833796986" duration="2621000" />
      <workItem from="1749105276767" duration="1227000" />
      <workItem from="1749120145115" duration="1745000" />
      <workItem from="1749157433889" duration="1798000" />
      <workItem from="1749248644726" duration="1829000" />
      <workItem from="1749368892694" duration="5519000" />
      <workItem from="1749434492709" duration="1324000" />
      <workItem from="1749468731779" duration="1202000" />
      <workItem from="1749520236383" duration="4470000" />
      <workItem from="1749642964132" duration="770000" />
      <workItem from="1749717988059" duration="1236000" />
      <workItem from="1749759244006" duration="2399000" />
      <workItem from="1749978308219" duration="1535000" />
      <workItem from="1750053731339" duration="7859000" />
      <workItem from="1750127148385" duration="4754000" />
      <workItem from="1750216184497" duration="92000" />
      <workItem from="1750216284970" duration="2005000" />
      <workItem from="1750302528619" duration="2462000" />
      <workItem from="1750379167748" duration="3727000" />
      <workItem from="1750456869673" duration="2399000" />
      <workItem from="1750543383069" duration="2563000" />
      <workItem from="1750676438225" duration="658000" />
      <workItem from="1750725549357" duration="1221000" />
      <workItem from="1750811343892" duration="1772000" />
      <workItem from="1750936066817" duration="1699000" />
      <workItem from="1750974362183" duration="487000" />
      <workItem from="1751023482746" duration="1207000" />
      <workItem from="1751061736348" duration="1653000" />
      <workItem from="1751186443780" duration="2357000" />
      <workItem from="1751250529307" duration="18000" />
      <workItem from="1751250597792" duration="602000" />
      <workItem from="1751369471077" duration="1751000" />
      <workItem from="1751490573476" duration="672000" />
      <workItem from="1751511466215" duration="2398000" />
      <workItem from="1751624233904" duration="2570000" />
      <workItem from="1751671675342" duration="1215000" />
      <workItem from="1751753042753" duration="3005000" />
      <workItem from="1751847710644" duration="1937000" />
      <workItem from="1751886817760" duration="1713000" />
      <workItem from="1751923488186" duration="1283000" />
      <workItem from="1751933540999" duration="2006000" />
      <workItem from="1752105409579" duration="4069000" />
      <workItem from="1752271348503" duration="3009000" />
      <workItem from="1752355542208" duration="3015000" />
      <workItem from="1752626985443" duration="1998000" />
      <workItem from="1752712310243" duration="3327000" />
      <workItem from="1752808781155" duration="3766000" />
      <workItem from="1752874578976" duration="4014000" />
      <workItem from="1752963430720" duration="9333000" />
      <workItem from="1753074382360" duration="1198000" />
      <workItem from="1753182633128" duration="898000" />
      <workItem from="1753246946392" duration="1069000" />
      <workItem from="1753305614239" duration="2888000" />
      <workItem from="1753391918835" duration="3144000" />
      <workItem from="1753480303029" duration="1593000" />
      <workItem from="1753528113596" duration="1523000" />
      <workItem from="1753566180179" duration="3653000" />
      <workItem from="1753663129355" duration="1207000" />
      <workItem from="1753747467877" duration="3172000" />
      <workItem from="1753824508740" duration="30000" />
      <workItem from="1753824548058" duration="4240000" />
      <workItem from="1753912723447" duration="1234000" />
      <workItem from="1753959849221" duration="1169000" />
      <workItem from="1753995785330" duration="3674000" />
      <workItem from="1754085612020" duration="1925000" />
      <workItem from="1754210039781" duration="1799000" />
      <workItem from="1754258900270" duration="31000" />
      <workItem from="1754258944278" duration="2715000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/nfo文件整理器.py</url>
          <line>13</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/规范百度网盘下载的文件名.py</url>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/$2.coverage" NAME="2 覆盖结果" MODIFIED="1735260280680" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$5.coverage" NAME="5 覆盖结果" MODIFIED="1708821940409" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$strm.coverage" NAME="strm生成器 覆盖结果" MODIFIED="1752144223108" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$pyton.coverage" NAME="pyton本地服务器 覆盖结果" MODIFIED="1714283170937" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$image_viewer.coverage" NAME="image_viewer 覆盖结果" MODIFIED="1737238374409" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$tdl.coverage" NAME="tdl前端 覆盖结果" MODIFIED="1722472815528" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$13.coverage" NAME="13 覆盖结果" MODIFIED="1718700655879" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$javbus_1.coverage" NAME="javbus无码-1 覆盖结果" MODIFIED="1715566208584" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$rename_video.coverage" NAME="rename_video 覆盖结果" MODIFIED="1717724976008" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$image_handle_1.coverage" NAME="image_handle-1 覆盖结果" MODIFIED="1733177090523" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$javbus.coverage" NAME="javbus无码刮削 覆盖结果" MODIFIED="1734568704376" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$auto_copy.coverage" NAME="auto_copy 覆盖结果" MODIFIED="1754085617532" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$12.coverage" NAME="12 覆盖结果" MODIFIED="1721184651093" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$.coverage" NAME="脚本快捷导航 覆盖结果" MODIFIED="1754278357727" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$porhub_.coverage" NAME="porhub 视频播放 覆盖结果" MODIFIED="1734606184069" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$3.coverage" NAME="3 覆盖结果" MODIFIED="1730877585591" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$jukujo.coverage" NAME="jukujo刮削 覆盖结果" MODIFIED="1717041872448" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$find_same_size_files.coverage" NAME="find_same_size_files 覆盖结果" MODIFIED="1748039317413" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$symlinks.coverage" NAME="symlinks 覆盖结果" MODIFIED="1721713211095" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$11.coverage" NAME="11 覆盖结果" MODIFIED="1726731322873" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$4.coverage" NAME="4 覆盖结果" MODIFIED="1716515182574" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$6.coverage" NAME="6 覆盖结果" MODIFIED="1708763847528" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$javdb.coverage" NAME="javdb刮削 覆盖结果" MODIFIED="1736152546353" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$1.coverage" NAME="1 覆盖结果" MODIFIED="1735286463435" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$nfo.coverage" NAME="nfo文件整理器 覆盖结果" MODIFIED="1736744060880" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$14.coverage" NAME="14 覆盖结果" MODIFIED="1709633596562" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$wmv.coverage" NAME="合并wmv 覆盖结果" MODIFIED="1709342916879" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/$_.coverage" NAME="整理文件夹 将名字中的指定字符移除 覆盖结果" MODIFIED="1714111521722" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>