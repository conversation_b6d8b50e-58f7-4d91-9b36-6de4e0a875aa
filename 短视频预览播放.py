# -*- coding: utf-8 -*-

import os
import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import shutil  # 添加这一行
import time
import psutil  # 添加这一行
import csv
import vlc
import threading
from queue import Queue
import subprocess
import sys
from concurrent.futures import ThreadPoolExecutor
import locale
import ctypes
from ctypes import wintypes

# 设置默认编码为UTF-8
if sys.version_info[0] == 3:
    sys.stdout.reconfigure(encoding='utf-8')
    sys.stderr.reconfigure(encoding='utf-8')

# 定义 Windows API 常量
MOVEFILE_COPY_ALLOWED = 0x2
MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
MOVEFILE_REPLACE_EXISTING = 0x1
MOVEFILE_WRITE_THROUGH = 0x8

# 加载 kernel32.dll
kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)

# 定义 MoveFileEx 函数
MoveFileEx = kernel32.MoveFileExW
MoveFileEx.argtypes = [wintypes.LPCWSTR, wintypes.LPCWSTR, wintypes.DWORD]
MoveFileEx.restype = wintypes.BOOL

def move_file_with_system(src, dst):
    """使用 Windows API 移动文件或文件夹，支持跨分区移动"""
    try:
        # 确保路径是绝对路径
        src = os.path.abspath(src)
        dst = os.path.abspath(dst)

        # 转换路径为 Windows 格式
        src = src.replace('/', '\\')
        dst = dst.replace('/', '\\')

        # 首先尝试使用 MoveFileEx
        result = MoveFileEx(src, dst, MOVEFILE_REPLACE_EXISTING | MOVEFILE_WRITE_THROUGH)

        if not result:
            error = ctypes.get_last_error()
            if error == 17:  # ERROR_NOT_SAME_DEVICE - 跨分区移动
                print(f"检测到跨分区移动，使用复制+删除方式: {src} -> {dst}")
                # 使用 shutil.move 进行跨分区移动
                shutil.move(src, dst)
                return True
            elif error == 32:  # ERROR_SHARING_VIOLATION
                raise Exception("文件被占用，无法移动")
            elif error == 5:  # ERROR_ACCESS_DENIED
                raise Exception("没有足够的权限移动文件")
            else:
                # 对于其他错误，也尝试使用 shutil.move
                print(f"MoveFileEx失败(错误代码: {error})，尝试使用shutil.move")
                shutil.move(src, dst)
                return True

        return True
    except Exception as e:
        raise Exception(f"移动文件失败: {str(e)}")

def delete_file_with_system(file_path):
    """使用多种方法删除文件，支持特殊字符"""
    try:
        # 确保路径是绝对路径
        file_path = os.path.abspath(file_path)

        # 转换路径为 Windows 格式
        file_path = file_path.replace('/', '\\')

        # 首先尝试使用标准方法
        try:
            os.remove(file_path)
            # 验证文件是否真的被删除
            if not os.path.exists(file_path):
                return True
            else:
                print(f"标准删除方法返回成功但文件仍存在: {file_path}")
        except (OSError, PermissionError) as e:
            print(f"标准删除方法失败: {e}，尝试使用系统方法")

        # 使用 Windows API 删除文件
        try:
            # 定义 DeleteFile 函数
            DeleteFile = ctypes.windll.kernel32.DeleteFileW
            DeleteFile.argtypes = [wintypes.LPCWSTR]
            DeleteFile.restype = wintypes.BOOL

            result = DeleteFile(file_path)

            if result:
                # 验证文件是否真的被删除
                if not os.path.exists(file_path):
                    return True
                else:
                    print(f"API删除返回成功但文件仍存在: {file_path}")
            else:
                error = ctypes.get_last_error()
                if error == 2:  # ERROR_FILE_NOT_FOUND
                    # 文件不存在，认为删除成功
                    return True
                elif error == 5:  # ERROR_ACCESS_DENIED
                    print(f"API删除失败: 没有足够的权限删除文件，错误代码: {error}")
                elif error == 32:  # ERROR_SHARING_VIOLATION
                    print(f"API删除失败: 文件被占用，错误代码: {error}")
                else:
                    print(f"API删除失败: 删除文件失败，错误代码: {error}")
        except Exception as api_error:
            print(f"API删除异常: {api_error}")

        # 尝试使用系统命令删除文件
        try:
            print(f"尝试使用系统命令删除: {file_path}")
            # 使用引号包围路径以处理空格和特殊字符
            quoted_path = f'"{file_path}"'
            result = subprocess.run(['cmd', '/c', 'del', '/f', '/q', quoted_path],
                                  capture_output=True, text=True, timeout=30)

            # 检查命令是否成功执行
            if result.returncode == 0:
                # 验证文件是否真的被删除
                if not os.path.exists(file_path):
                    print(f"系统命令删除成功: {file_path}")
                    return True
                else:
                    print(f"系统命令返回成功但文件仍存在: {file_path}")
            else:
                print(f"系统命令删除失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"系统命令删除超时: {file_path}")
        except Exception as cmd_error:
            print(f"系统命令删除异常: {cmd_error}")

        # 最后尝试使用 PowerShell 删除
        try:
            print(f"尝试使用 PowerShell 删除: {file_path}")
            ps_command = f'Remove-Item -Path "{file_path}" -Force'
            result = subprocess.run(['powershell', '-Command', ps_command],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                if not os.path.exists(file_path):
                    print(f"PowerShell 删除成功: {file_path}")
                    return True
                else:
                    print(f"PowerShell 返回成功但文件仍存在: {file_path}")
            else:
                print(f"PowerShell 删除失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"PowerShell 删除超时: {file_path}")
        except Exception as ps_error:
            print(f"PowerShell 删除异常: {ps_error}")

        # 如果所有方法都失败，抛出异常
        raise Exception(f"所有删除方法都失败，文件可能被锁定或权限不足: {file_path}")

    except Exception as e:
        raise Exception(f"删除文件失败: {str(e)}")

def delete_folder_with_system(folder_path):
    """使用多种方法删除文件夹，支持特殊字符"""
    try:
        # 确保路径是绝对路径
        folder_path = os.path.abspath(folder_path)

        # 转换路径为 Windows 格式
        folder_path = folder_path.replace('/', '\\')

        print(f"开始删除文件夹: {folder_path}")

        # 首先尝试使用标准方法
        try:
            shutil.rmtree(folder_path)
            if not os.path.exists(folder_path):
                print(f"标准方法删除成功: {folder_path}")
                return True
            else:
                print(f"标准方法返回成功但文件夹仍存在: {folder_path}")
        except (OSError, PermissionError) as e:
            print(f"标准删除方法失败: {e}，尝试逐个删除文件")

        # 尝试使用系统命令删除整个文件夹
        try:
            print(f"尝试使用系统命令删除文件夹: {folder_path}")
            # 使用引号包围路径以处理空格和特殊字符
            quoted_path = f'"{folder_path}"'
            result = subprocess.run(['cmd', '/c', 'rmdir', '/s', '/q', quoted_path],
                                  capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                if not os.path.exists(folder_path):
                    print(f"系统命令删除文件夹成功: {folder_path}")
                    return True
                else:
                    print(f"系统命令返回成功但文件夹仍存在: {folder_path}")
            else:
                print(f"系统命令删除文件夹失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"系统命令删除文件夹超时: {folder_path}")
        except Exception as cmd_error:
            print(f"系统命令删除文件夹异常: {cmd_error}")

        # 如果系统命令失败，尝试逐个删除文件和子文件夹
        try:
            print(f"尝试逐个删除文件和子文件夹: {folder_path}")
            # 递归删除所有文件和子文件夹
            for root, dirs, files in os.walk(folder_path, topdown=False):
                # 删除所有文件
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        delete_file_with_system(file_path)
                    except Exception as e:
                        print(f"删除文件失败: {file_path}, 错误: {e}")
                        # 继续删除其他文件
                        continue

                # 删除所有空文件夹
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        os.rmdir(dir_path)
                    except Exception as e:
                        print(f"删除子文件夹失败: {dir_path}, 错误: {e}")
                        # 尝试使用系统命令删除
                        try:
                            quoted_dir = f'"{dir_path}"'
                            subprocess.run(['cmd', '/c', 'rmdir', '/s', '/q', quoted_dir],
                                         capture_output=True, text=True, timeout=30)
                        except:
                            continue

            # 最后删除根文件夹
            try:
                os.rmdir(folder_path)
                if not os.path.exists(folder_path):
                    print(f"逐个删除成功: {folder_path}")
                    return True
            except Exception as e:
                print(f"删除根文件夹失败: {e}")

        except Exception as e:
            print(f"逐个删除失败: {e}")

        # 最后尝试使用 PowerShell 删除
        try:
            print(f"尝试使用 PowerShell 删除文件夹: {folder_path}")
            ps_command = f'Remove-Item -Path "{folder_path}" -Recurse -Force'
            result = subprocess.run(['powershell', '-Command', ps_command],
                                  capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                if not os.path.exists(folder_path):
                    print(f"PowerShell 删除文件夹成功: {folder_path}")
                    return True
                else:
                    print(f"PowerShell 返回成功但文件夹仍存在: {folder_path}")
            else:
                print(f"PowerShell 删除文件夹失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
        except subprocess.TimeoutExpired:
            print(f"PowerShell 删除文件夹超时: {folder_path}")
        except Exception as ps_error:
            print(f"PowerShell 删除文件夹异常: {ps_error}")

        # 如果所有方法都失败，抛出异常
        raise Exception(f"所有删除方法都失败，文件夹可能包含被锁定的文件或权限不足: {folder_path}")

    except Exception as e:
        raise Exception(f"删除文件夹失败: {str(e)}")

class FileManager:
    def __init__(self, video_player):
        self.video_player = video_player

    def delete_video(self, video_path):
        """
        删除指定的视频文件
        :param video_path: 视频文件的路径
        """
        try:
            # 检查文件是否存在
            if os.path.exists(video_path):
                # 使用支持特殊字符的删除方法
                self.stop_video()
                delete_file_with_system(video_path)
                print(f"文件已删除: {video_path}")
            else:
                print(f"文件不存在: {video_path}")
        except Exception as e:
            print(f"删除文件时出错: {str(e)}")

    def delete_folder(self, folder_path):
        """
        删除指定的文件夹及其内容
        :param folder_path: 文件夹路径
        """
        try:
            # 检查文件夹是否存在
            if os.path.exists(folder_path):
                # 使用支持特殊字符的删除方法
                delete_folder_with_system(folder_path)
                print(f"文件夹已删除: {folder_path}")
            else:
                print(f"文件夹不存在: {folder_path}")
        except Exception as e:
            print(f"删除文件夹时出错: {str(e)}")

    def stop_video(self):
        pass


class VideoPlayer:
    def __init__(self, master):
        self.master = master
        self.player = None
        self._is_playing = False
        self._window_exists = True
        self.frame_queue = Queue(maxsize=10)
        self.current_video_path = None  # 添加当前视频路径
        self.current_label = None  # 添加当前标签
        self.last_skip_time = 0  # 添加上次跳转时间记录
        self.video_duration = 0  # 添加视频总时长记录

        # 绑定窗口关闭事件
        self.master.bind('<Destroy>', self._on_window_destroy)

    def _on_window_destroy(self, event):
        """窗口关闭时的处理"""
        if event.widget == self.master:
            self._window_exists = False
            self.stop_video()

    def play_video(self, video_path, label):
        if self.current_video_path == video_path and self._is_playing and self.current_label == label:
            return

        self.stop_video()
        self.current_video_path = video_path
        self.current_label = label
        self._is_playing = True
        self.last_skip_time = time.time()  # 重置跳转时间

        # 获取视频时长
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.video_duration = total_frames / fps if fps > 0 else 0
        cap.release()

        def video_worker():
            cap = cv2.VideoCapture(video_path)
            
            # 获取原始分辨率
            orig_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            orig_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 根据分辨率动态调整跳帧率和缩放比例
            if orig_width * orig_height > 1920 * 1080:  # 高于1080p
                frame_skip = 10  # 增加跳帧
                scale_factor = 0.25  # 降低到1/4大小
            else:
                frame_skip = 5
                scale_factor = 1.0
            
            # 计算处理分辨率
            process_width = int(orig_width * scale_factor)
            process_height = int(orig_height * scale_factor)
            
            frame_count = 0

            while self._is_playing and self._window_exists:
                try:
                    if not label.winfo_exists():
                        break
                except:
                    break

                # 检查是否需要跳转
                current_time = time.time()
                if current_time - self.last_skip_time >= 5:
                    self.last_skip_time = current_time
                    current_frame = cap.get(cv2.CAP_PROP_POS_FRAMES)
                    skip_frames = int(total_frames / 7)
                    new_frame = min(current_frame + skip_frames, total_frames - 1)
                    cap.set(cv2.CAP_PROP_POS_FRAMES, new_frame)
                    
                    while not self.frame_queue.empty():
                        self.frame_queue.get()
                    frame_count = 0
                    continue

                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1
                if frame_count % frame_skip != 0:
                    continue

                # 先降低分辨率再处理
                if scale_factor != 1.0:
                    frame = cv2.resize(frame, (process_width, process_height))

                # 计算显示尺寸
                new_width, new_height = calculate_scaled_dimensions(process_width, process_height)

                # 缩放到显示尺寸
                frame = cv2.resize(frame, (new_width, new_height))
                
                # 创建背景
                background = Image.new('RGB', (400, 300), 'black')
                
                # 将视频帧放在中央
                thumbnail = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                x = (400 - new_width) // 2
                y = (300 - new_height) // 2
                background.paste(thumbnail, (x, y))

                if not self.frame_queue.full():
                    self.frame_queue.put(background)

            cap.release()

        def update_label():
            if not self._is_playing or self.current_label != label:
                return
            
            try:
                # 检查标签是否还存在
                if not label.winfo_exists():
                    return
                
                if not self.frame_queue.empty():
                    frame = self.frame_queue.get()
                    img = ImageTk.PhotoImage(frame)
                    label.config(image=img)
                    label.image = img
                
                # 只有在播放状态和标签存在时才继续更新
                if self._is_playing and label.winfo_exists():
                    self.master.after(30, update_label)
            except Exception as e:
                print(f"更新标签时出错: {e}")
                return

        threading.Thread(target=video_worker, daemon=True).start()
        update_label()

    def stop_video(self):
        self._is_playing = False
        self.current_video_path = None
        self.current_label = None
        # 清空帧队列
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except:
                pass





def calculate_scaled_dimensions(width, height, max_width=400, max_height=300):
    """计算等比缩放后的尺寸"""
    if width <= 0 or height <= 0:
        return max_width, max_height

    aspect_ratio = width / height

    if width > max_width or height > max_height:
        if aspect_ratio > max_width / max_height:  # 宽度限制
            new_width = max_width
            new_height = int(max_width / aspect_ratio)
        else:  # 高度限制
            new_height = max_height
            new_width = int(max_height * aspect_ratio)
    else:
        new_width, new_height = width, height

    return new_width, new_height

class VideoPreviewer:
    def __init__(self, master):
        self.master = master
        self.master.title("Video Previewer")
        
        # 将自身实例设为主窗口的属性，便于其他组件访问
        self.master.videoPreviewer = self

        # 设置主窗口大小
        self.master.geometry("1600x900")  # 进一步增加窗口宽度以显示所有按钮

        self.folder_path = ""  # 初始化为空字符串
        self.current_page = 1
        self.videos_per_page = 12
        self.videos_per_row = 4  # 每行显示4个视频

        self.thumbnail_labels = []

        # 确保 VideoPlayer 被正确初始化
        self.video_player = VideoPlayer(master)
        self.file_manager = FileManager(self.video_player)

        # 添加 self.video_files
        self.video_files = []

        # 初始化复选框变量字典
        self.checkbox_vars = {}

        # 添加排序方式变量
        self.sort_method = tk.StringVar(value="size")  # 默认按文件大小排序
        self.sort_reverse = tk.BooleanVar(value=True)  # 默认降序（从大到小）
        self.use_similarity = tk.BooleanVar(value=False)  # 是否使用相似度排序

        # 添加像素过滤变量
        self.pixel_filter = tk.StringVar(value="all")  # 像素过滤选项：all, 0.8M, 2M, 4M，默认显示全部
        self.pixel_filter_mode = tk.StringVar(value="below")  # 过滤模式：above（之上）, below（之下）

        # 添加缩略图缓存
        self.thumbnail_cache = {}

        # 添加像素缓存
        self.pixel_cache = {}

        # 添加视频信息缓存
        self.video_info_cache = {}

        # 添加移动历史记录
        self.move_history = self.load_move_history()

        # 添加缩放计算函数
        self.calculate_scaled_dimensions = calculate_scaled_dimensions

        # 设置日志文件
        self.log_dir = "logs"  # 日志目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        self.log_file = os.path.join(self.log_dir, "delete_log.txt")

        # 添加删除记录文件路径
        self.index_path = "Z:/work/deleted_index.txt"

        # 修改文件路径记录相关设置
        self.last_folder_file = "last_folder.txt"
        self.default_folder_file = "default_folder.txt"  # 添加默认文件夹记录文件

        # 修改图片查看器路径
        self.image_viewer = os.path.join(os.path.dirname(__file__), "图片预览查看管理器-简版.py")

        # 创建界面组件
        self.create_widgets()

        # 自动加载上次打开的文件夹
        self.auto_load_last_folder()

    def auto_load_last_folder(self):
        """自动加载上次打开的文件夹"""
        try:
            last_folder = self.load_last_folder()
            if last_folder and os.path.exists(last_folder):
                self.folder_path = last_folder
                # 更新文件夹名显示
                self.folder_label.config(text=f"当前文件夹: {os.path.basename(last_folder)}")
                # 重置页码
                self.current_page = 1
                # 显示预览（增强错误处理）
                try:
                    self.show_video_preview(force_rescan=True)  # 启动时强制重新扫描
                except Exception as preview_error:
                    print(f"显示视频预览失败: {preview_error}")
                    # 即使预览失败，程序也应该能正常启动
        except Exception as e:
            print(f"自动加载上次文件夹失败: {e}")
            # 确保程序能正常启动，即使加载失败

    def get_video_pixels(self, video_path):
        """获取视频的像素数量（带缓存）"""
        # 检查缓存
        if video_path in self.pixel_cache:
            return self.pixel_cache[video_path]

        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                pixels = 0
            else:
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                pixels = width * height
                cap.release()

            # 缓存结果
            self.pixel_cache[video_path] = pixels
            return pixels
        except Exception:
            # 缓存失败结果
            self.pixel_cache[video_path] = 0
            return 0

    def apply_pixel_filter(self):
        """应用像素过滤并立即更新界面"""
        if self.folder_path:
            # 清理像素缓存以确保重新计算
            self.pixel_cache.clear()
            # 重置到第一页
            self.current_page = 1
            # 清理当前显示（保留缩略图和视频信息缓存）
            self.clear_canvas()
            # 强制重新扫描以应用新的过滤条件
            self.show_video_preview(force_rescan=True)

    def create_widgets(self):
        # 创建顶部标签显示当前文件夹名
        self.folder_label = tk.Label(self.master, text="当前文件夹: 未选择", anchor="w")
        self.folder_label.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)

        # 为文件夹标签创建右键菜单
        self.folder_context_menu = tk.Menu(self.master, tearoff=0)
        self.folder_context_menu.add_command(label="重命名文件夹", command=self.rename_current_folder)

        # 绑定右键菜单到文件夹标签
        self.folder_label.bind("<Button-3>", self.show_folder_context_menu)

        # 创建像素过滤框架
        self.pixel_filter_frame = tk.LabelFrame(self.master, text="像素过滤")
        self.pixel_filter_frame.pack(fill=tk.X, padx=10, pady=5)

        # 创建像素过滤控件
        filter_row = tk.Frame(self.pixel_filter_frame)
        filter_row.pack(fill=tk.X, padx=5, pady=5)

        # 像素选项
        tk.Radiobutton(filter_row, text="全部", variable=self.pixel_filter,
                      value="all", command=self.apply_pixel_filter).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(filter_row, text="80万像素", variable=self.pixel_filter,
                      value="0.8M", command=self.apply_pixel_filter).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(filter_row, text="200万像素", variable=self.pixel_filter,
                      value="2M", command=self.apply_pixel_filter).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(filter_row, text="400万像素", variable=self.pixel_filter,
                      value="4M", command=self.apply_pixel_filter).pack(side=tk.LEFT, padx=5)

        # 分隔线
        tk.Label(filter_row, text=" | ").pack(side=tk.LEFT, padx=5)

        # 过滤模式选项
        tk.Radiobutton(filter_row, text="之上", variable=self.pixel_filter_mode,
                      value="above", command=self.apply_pixel_filter).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(filter_row, text="之下", variable=self.pixel_filter_mode,
                      value="below", command=self.apply_pixel_filter).pack(side=tk.LEFT, padx=5)

        # 创建主框架来容纳所有元素
        main_frame = tk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建画布框架，设置合适的高度
        canvas_frame = tk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(5, 0))

        # 创建画布和滚动条
        self.canvas = tk.Canvas(canvas_frame, bg='white')
        scrollbar = tk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 放置画布和滚动条
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建一个框架来容纳视频缩略图
        self.content_frame = tk.Frame(self.canvas)
        self.canvas.create_window((0, 0), window=self.content_frame, anchor="nw")

        # 绑定鼠标滚轮事件
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)  # Windows
        self.canvas.bind_all("<Button-4>", self._on_mousewheel)    # Linux
        self.canvas.bind_all("<Button-5>", self._on_mousewheel)    # Linux

        # 创建按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

        # 添加按钮（适当增加间距以利用更宽的窗口）
        prev_button = tk.Button(button_frame, text="上一页", command=self.show_prev_page)
        prev_button.pack(side=tk.LEFT, padx=(10, 5))

        open_button = tk.Button(button_frame, text="打开", command=self.open_folder)
        open_button.pack(side=tk.LEFT, padx=5)

        # 添加刷新按钮
        refresh_button = tk.Button(button_frame, text="刷新", command=self.force_refresh_current_page)
        refresh_button.pack(side=tk.LEFT, padx=5)

        next_button = tk.Button(button_frame, text="下一页", command=self.show_next_page)
        next_button.pack(side=tk.LEFT, padx=5)

        # 添加页码显示标签
        self.page_label = tk.Label(button_frame, text="0/0")
        self.page_label.pack(side=tk.LEFT, padx=8)

        delete_button = tk.Button(button_frame, text="删除选中", command=self.delete_selected_videos)
        delete_button.pack(side=tk.LEFT, padx=5)

        # 添加全选按钮
        select_all_button = tk.Button(button_frame, text="全选当前页", command=self.toggle_select_all)
        select_all_button.pack(side=tk.LEFT, padx=5)

        # 添加移动选中按钮
        move_selected_button = tk.Button(button_frame, text="移动选中", command=self.show_move_selected_dialog)
        move_selected_button.pack(side=tk.LEFT, padx=5)

        # 添加保留选中按钮
        keep_selected_button = tk.Button(button_frame, text="保留选中", command=self.keep_selected_videos)
        keep_selected_button.pack(side=tk.LEFT, padx=5)

        # 添加移动文件夹按钮
        move_folder_button = tk.Button(button_frame, text="移动文件夹", command=self.show_move_folder_dialog)
        move_folder_button.pack(side=tk.LEFT, padx=5)

        # 添加删除文件夹按钮（保持一定距离突出危险性）
        delete_folder_button = tk.Button(button_frame, text="删除文件夹", command=self.confirm_delete_folder,
                                       fg='red')  # 使用红色文字表示危险操作
        delete_folder_button.pack(side=tk.LEFT, padx=(15, 5))

        # 在按钮框架中添加排序控件
        sort_frame = tk.Frame(button_frame)
        sort_frame.pack(side=tk.LEFT, padx=8)

        # 排序方式选择
        sort_label = tk.Label(sort_frame, text="排序:")
        sort_label.pack(side=tk.LEFT, padx=(0, 3))

        sort_methods = [
            ("文件名", "name"),
            ("相关性", "similarity"),  # 添加相关性排序选项
            ("大小", "size"),
            ("时长", "duration"),
            ("修改时间", "mtime")
        ]

        for text, value in sort_methods:
            rb = tk.Radiobutton(sort_frame, text=text,
                              variable=self.sort_method,
                              value=value,
                              command=self.resort_videos)
            rb.pack(side=tk.LEFT, padx=2)

        # 升序/降序切换
        self.order_button = tk.Button(sort_frame,
                                    text="↓降序",
                                    command=self.toggle_sort_order)
        self.order_button.pack(side=tk.LEFT, padx=5)

        # 在按钮框架中添加打开图片按钮
        open_images_button = tk.Button(button_frame, text="打开图片", command=self.open_images)
        open_images_button.pack(side=tk.LEFT, padx=5)

        # 绑定画布大小变化事件
        self.content_frame.bind("<Configure>", self.on_frame_configure)

        # 创建右键菜单
        self.context_menu = tk.Menu(self.master, tearoff=0)
        self.context_menu.add_command(label="复制文件名", command=self.copy_filename)
        self.context_menu.add_command(label="重命名", command=self.rename_file)
        self.context_menu.add_separator()  # 添加分隔线
        self.context_menu.add_command(label="移动文件", command=self.move_current_file)  # 添加移动文件选项
        self.context_menu.add_separator()  # 添加分隔线
        self.context_menu.add_command(label="删除", command=self.delete_current_file, foreground='red')  # 添加删除选项，红色显示

    def toggle_select_all(self):
        """切换当前页所有视频的选中状态"""
        # 检查当前页是否全部选中
        current_page_vars = []
        start_index = (self.current_page - 1) * self.videos_per_page
        end_index = start_index + self.videos_per_page
        
        # 获取当前页的视频路径
        current_page_videos = self.video_files[start_index:end_index]
        for video in current_page_videos:
            video_path = os.path.join(self.folder_path, video)
            if video_path in self.checkbox_vars:
                current_page_vars.append(self.checkbox_vars[video_path])
        
        # 检查当前状态
        all_selected = all(var.get() for var in current_page_vars)
        
        # 切换状态
        new_state = not all_selected
        for var in current_page_vars:
            var.set(new_state)

    def open_folder(self):
        """打开文件夹"""
        # 如果是第一次打开（没有默认文件夹记录），使用当前路径或根目录
        if not os.path.exists(self.default_folder_file):
            initial_dir = self.folder_path if self.folder_path else "/"
        else:
            # 非第一次打开，总是使用默认文件夹作为初始目录
            initial_dir = self.load_default_folder() or "/"

        folder = filedialog.askdirectory(initialdir=initial_dir)

        if folder:
            self.folder_path = folder
            # 更新文件夹名显示
            self.folder_label.config(text=f"当前文件夹: {os.path.basename(folder)}")
            # 保存当前文件夹路径
            self.save_last_folder(folder)
            # 尝试保存父文件夹为默认文件夹（如果还没有设置的话）
            self.save_default_folder(folder)

            # 统计图片数量
            image_count = self.count_images(folder)
            messagebox.showinfo("图片统计", f"文件夹及子文件夹中共有 {image_count} 张图片")

            # 重置页码
            self.current_page = 1
            # 显示预览
            self.show_video_preview(force_rescan=True)  # 打开新文件夹时强制重新扫描

    def count_images(self, folder_path):
        """统计文件夹及子文件夹中的图片数量"""
        image_count = 0
        image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')

        # 遍历文件夹及其子文件夹
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith(image_extensions):
                    image_count += 1

        return image_count

    def show_preview(self, event, video_path, thumbnail_image):
        try:
            # 检查窗口和标签是否还存在
            if not self.master.winfo_exists():
                return

            # 如果是同一个视频，不重新加载
            if hasattr(self, '_current_preview') and self._current_preview == video_path:
                return

            # 停止之前的预览并清理资源
            self.video_player.stop_video()

            # 清理标签的图像
            event.widget.configure(image=thumbnail_image)
            event.widget.image = thumbnail_image

            # 等待资源释放
            time.sleep(0.1)

            self._current_preview = video_path

            # 使用线程来加载预览，避免界面卡顿
            def load_preview():
                try:
                    if not self.master.winfo_exists():
                        return

                    # 开始新的预览
                    self.video_player.play_video(video_path, event.widget)
                except Exception as e:
                    print(f"加载预览时出错: {e}")

            threading.Thread(target=load_preview, daemon=True).start()

        except Exception as e:
            print(f"预览视频时出错: {e}")

    def generate_thumbnail(self, video_path):
        """优化后的预览图生成，确保总是返回有效图像"""
        # 检查缓存
        if video_path in self.thumbnail_cache:
            return self.thumbnail_cache[video_path]

        # 检查文件是否存在
        if not os.path.exists(video_path):
            print(f"视频文件不存在: {video_path}")
            return self._generate_default_thumbnail()

        try:
            # 动态调整线程池大小
            if not hasattr(self, '_thumbnail_executor'):
                cpu_count = os.cpu_count() or 4
                self._thumbnail_executor = ThreadPoolExecutor(max_workers=min(cpu_count * 2, 8))

            # 根据文件大小动态调整超时时间（增加超时时间）
            try:
                file_size = os.path.getsize(video_path)
                if file_size < 50 * 1024 * 1024:  # 50MB以下
                    timeout = 5
                elif file_size < 200 * 1024 * 1024:  # 200MB以下
                    timeout = 10
                else:  # 大文件
                    timeout = 15
            except OSError:
                print(f"无法获取文件大小: {video_path}")
                timeout = 10  # 使用更长的默认超时时间

            future = self._thumbnail_executor.submit(self._generate_thumbnail_worker, video_path)
            try:
                thumbnail = future.result(timeout=timeout)
            except TimeoutError:
                print(f"缩略图生成超时: {os.path.basename(video_path)}")
                return self._generate_default_thumbnail()

            if thumbnail is None:

                # 尝试备用的简单方法
                thumbnail = self._generate_simple_thumbnail(video_path)
                if thumbnail is None:
                    return self._generate_default_thumbnail()

            # 缩略图已经在worker中按比例缩放并放置在固定框架中，直接缓存并返回
            self.thumbnail_cache[video_path] = thumbnail
            return thumbnail

        except Exception as e:
            print(f"生成缩略图失败: {video_path}, 错误: {e}")
            # 对于失败的视频文件，提供诊断信息
            if hasattr(self, 'diagnose_video_file'):
                try:
                    self.diagnose_video_file(video_path)
                except:
                    pass  # 诊断失败不影响主流程
            # 确保总是返回一个有效的默认图像
            return self._generate_default_thumbnail()

    def _generate_default_thumbnail(self):
        """生成默认的黑色缩略图，确保总是返回有效图像"""
        try:
            # 创建黑色背景图片
            background = Image.new('RGB', (400, 300), 'black')
            try:
                # 尝试添加文本提示
                from PIL import ImageDraw
                draw = ImageDraw.Draw(background)
                text = "无法加载预览"
                # 计算文本位置使其居中
                try:
                    text_bbox = draw.textbbox((0, 0), text)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                    x = (400 - text_width) // 2
                    y = (300 - text_height) // 2
                    draw.text((x, y), text, fill='white')
                except:
                    # 如果textbbox不可用，使用简单的居中方式
                    draw.text((150, 140), text, fill='white')
            except ImportError:
                # 如果PIL.ImageDraw不可用，返回纯黑色图片
                pass
            return background
        except Exception as e:
            print(f"生成默认缩略图失败: {e}")
            try:
                # 最后的备用方案：创建最简单的黑色图片
                return Image.new('RGB', (400, 300), 'black')
            except:
                # 如果连这个都失败，返回None（调用方需要处理）
                print("无法创建任何图像，返回None")
                return None

    def _generate_thumbnail_worker(self, video_path):
        """优化后的缩略图生成工作函数"""
        try:
            # 检查文件是否存在
            if not os.path.exists(video_path):
                print(f"视频文件不存在: {video_path}")
                return None

            # 检查文件大小
            try:
                file_size = os.path.getsize(video_path)
                if file_size == 0:
                    print(f"视频文件为空: {video_path}")
                    return None
            except OSError as e:
                print(f"无法获取文件大小: {video_path}, 错误: {e}")
                return None

            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                print(f"OpenCV无法打开视频文件: {os.path.basename(video_path)}")
                return None

            # 获取视频总帧数
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            if total_frames <= 0:
                print(f"视频没有有效帧: {os.path.basename(video_path)}")
                return None

            # 如果视频较长，跳转到中间位置（但不要跳转太远，避免某些格式的问题）
            if total_frames > 100:
                # 跳转到前1/4位置而不是中间，某些视频格式在中间位置可能有问题
                target_frame = min(total_frames // 4, 300)  # 最多跳转300帧
                video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)

            ret, frame = video_capture.read()
            if not ret:
                print(f"无法读取视频帧: {os.path.basename(video_path)}")
                return None

            # 检查帧是否有效
            if frame is None or frame.size == 0:
                print(f"读取到的视频帧无效: {os.path.basename(video_path)}")
                return None
                
            # 获取原始视频尺寸
            orig_height, orig_width = frame.shape[:2]

            # 计算按比例缩放后的尺寸
            new_width, new_height = self.calculate_scaled_dimensions(orig_width, orig_height)

            # 按比例缩放帧
            frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 创建固定大小的背景框（400x300）
            background = Image.new('RGB', (400, 300), 'black')

            # 将按比例缩放的视频帧居中放置在背景框中
            thumbnail = Image.fromarray(frame)
            x = (400 - new_width) // 2
            y = (300 - new_height) // 2
            background.paste(thumbnail, (x, y))

            return background

        except Exception as e:
            print(f"生成缩略图失败: {os.path.basename(video_path)}, 错误: {e}")
            # 提供更详细的错误信息
            if "codec" in str(e).lower():
                print(f"  可能的原因: 不支持的视频编解码器")
            elif "format" in str(e).lower():
                print(f"  可能的原因: 不支持的视频格式")
            elif "corrupt" in str(e).lower():
                print(f"  可能的原因: 视频文件损坏")
            else:
                print(f"  建议: 检查视频文件是否完整且格式正确")
            return None
        finally:
            if 'video_capture' in locals():
                try:
                    video_capture.release()
                    # 强制OpenCV释放资源
                    cv2.waitKey(1)
                    cv2.destroyAllWindows()
                except:
                    pass

    def _generate_simple_thumbnail(self, video_path):
        """备用的简单缩略图生成方法，不使用线程池"""
        try:


            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                return None

            # 简单方法：只读取第一帧，不跳转
            ret, frame = video_capture.read()
            if not ret or frame is None:
                return None

            # 获取原始视频尺寸
            orig_height, orig_width = frame.shape[:2]

            # 计算按比例缩放后的尺寸
            new_width, new_height = self.calculate_scaled_dimensions(orig_width, orig_height)

            # 按比例缩放帧
            frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 创建固定大小的背景框（400x300）
            background = Image.new('RGB', (400, 300), 'black')

            # 将按比例缩放的视频帧居中放置在背景框中
            thumbnail = Image.fromarray(frame)
            x = (400 - new_width) // 2
            y = (300 - new_height) // 2
            background.paste(thumbnail, (x, y))


            return background

        except Exception as e:
            print(f"备用方法也失败: {os.path.basename(video_path)}, 错误: {e}")
            return None
        finally:
            if 'video_capture' in locals():
                video_capture.release()

    def diagnose_video_file(self, video_path):
        """诊断视频文件问题"""
        try:
            print(f"\n=== 诊断视频文件: {os.path.basename(video_path)} ===")

            # 检查文件基本信息
            if not os.path.exists(video_path):
                print("❌ 文件不存在")
                return False

            file_size = os.path.getsize(video_path)
            print(f"📁 文件大小: {file_size / (1024*1024):.2f} MB")

            if file_size == 0:
                print("❌ 文件为空")
                return False

            # 检查文件扩展名
            ext = os.path.splitext(video_path)[1].lower()
            print(f"📄 文件扩展名: {ext}")

            # 尝试用OpenCV打开
            video_capture = cv2.VideoCapture(video_path)
            if not video_capture.isOpened():
                print("❌ OpenCV无法打开文件")
                print("   可能原因: 不支持的编解码器或文件损坏")
                return False

            # 获取视频属性
            fps = video_capture.get(cv2.CAP_PROP_FPS)
            frame_count = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(video_capture.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(video_capture.get(cv2.CAP_PROP_FRAME_HEIGHT))

            print(f"🎬 分辨率: {width}x{height}")
            print(f"🎬 帧率: {fps:.2f} FPS")
            print(f"🎬 总帧数: {frame_count}")

            if frame_count > 0 and fps > 0:
                duration = frame_count / fps
                print(f"⏱️ 时长: {duration:.2f} 秒")

            # 尝试读取第一帧
            ret, frame = video_capture.read()
            if ret and frame is not None:
                print("✅ 可以读取视频帧")
                return True
            else:
                print("❌ 无法读取视频帧")
                return False

        except Exception as e:
            print(f"❌ 诊断过程中出错: {e}")
            return False
        finally:
            if 'video_capture' in locals():
                video_capture.release()

    def get_video_files(self):
        """获取所有视频文件"""
        if not self.folder_path:
            return []

        video_files = []
        video_extensions = ('.mp4', '.avi', '.mkv', '.flv', '.wmv', '.mov', '.ts', '.m4v')

        # 遍历文件夹及其子文件夹
        for root, dirs, files in os.walk(self.folder_path):
            for file in files:
                if file.lower().endswith(video_extensions):
                    # 获取相对于主文件夹的路径
                    rel_path = os.path.relpath(os.path.join(root, file), self.folder_path)
                    video_files.append(rel_path)

        # 如果没有找到视频文件，显示提示
        if not video_files:
            messagebox.showinfo("提示", "当前文件夹及其子文件夹中未找到视频文件")
            return []

        # 应用像素过滤
        pixel_filter = self.pixel_filter.get()
        if pixel_filter != "all":
            filtered_files = []

            # 定义像素阈值
            pixel_thresholds = {
                "0.8M": 800000,  # 80万像素
                "2M": 2000000,   # 200万像素
                "4M": 4000000    # 400万像素
            }

            threshold = pixel_thresholds.get(pixel_filter, 0)
            filter_mode = self.pixel_filter_mode.get()

            for video_file in video_files:
                video_path = os.path.join(self.folder_path, video_file)
                pixels = self.get_video_pixels(video_path)

                if filter_mode == "above" and pixels >= threshold:
                    filtered_files.append(video_file)
                elif filter_mode == "below" and pixels < threshold:
                    filtered_files.append(video_file)

            video_files = filtered_files

        # 根据当前排序方式排序
        sort_method = self.sort_method.get()
        if sort_method == "name":
            video_files.sort(reverse=self.sort_reverse.get())
        elif sort_method == "size":
            video_files.sort(key=lambda x: os.path.getsize(os.path.join(self.folder_path, x)),
                           reverse=self.sort_reverse.get())
        elif sort_method == "mtime":
            video_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.folder_path, x)),
                           reverse=self.sort_reverse.get())
        elif sort_method == "duration":
            # 默认按时长从长到短排序
            video_files.sort(
                key=lambda x: self.get_video_info(os.path.join(self.folder_path, x))[3],
                reverse=True if not self.sort_reverse.get() else False
            )

        return video_files

    def get_video_info(self, video_path):
        """获取视频文件的大小、分辨率和时长（带缓存）"""
        # 检查缓存
        if video_path in self.video_info_cache:
            return self.video_info_cache[video_path]

        try:
            # 获取文件大小
            file_size = os.path.getsize(video_path)
            if file_size < 1024 * 1024:  # 小于1MB
                size_str = f"{file_size/1024:.1f}KB"
            else:  # 大于等于1MB
                size_str = f"{file_size/(1024*1024):.1f}MB"

            # 获取视频分辨率和时长
            cap = cv2.VideoCapture(video_path)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 获取时长
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            duration_str = self.format_duration(duration)

            cap.release()

            # 缓存结果
            result = (size_str, f"{width}x{height}", duration_str, duration)
            self.video_info_cache[video_path] = result
            return result
        except Exception as e:
            print(f"获取视频信息出错: {e}")
            # 缓存失败结果
            result = ("未知", "未知", "未知", 0)
            self.video_info_cache[video_path] = result
            return result

    def format_duration(self, seconds):
        """将秒数转换为时:分:秒格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

    def toggle_sort_order(self):
        """切换排序顺序"""
        self.sort_reverse.set(not self.sort_reverse.get())
        self.order_button.config(text="↓降序" if self.sort_reverse.get() else "↑升序")
        self.resort_videos()

    def resort_videos(self):
        """重新排序并刷新显示"""
        if self.folder_path:
            self.current_page = 1
            self.show_video_preview(force_rescan=True)  # 重新排序时需要重新扫描

    def show_prev_page(self):
        if self.current_page > 1:
            self.current_page -= 1
            # 使用缓存优化的页面切换
            self.fast_page_switch()

    def show_next_page(self):
        videos = self.get_video_files()
        total_pages = (len(videos) + self.videos_per_page - 1) // self.videos_per_page

        if self.current_page < total_pages:
            self.current_page += 1
            # 使用缓存优化的页面切换
            self.fast_page_switch()

    def fast_page_switch(self):
        """快速页面切换 - 充分利用缓存"""
        try:
            # 检查当前页面的缩略图是否都已缓存
            videos_to_display = self.get_current_page_videos()
            all_cached = True

            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)
                if video_path not in self.thumbnail_cache:
                    all_cached = False
                    break

            if all_cached and len(videos_to_display) > 0:
                # 如果都已缓存，使用快速显示
                self.fast_display_cached_page(videos_to_display)
            else:
                # 否则使用常规方式
                self.clear_canvas()
                self.show_video_preview()

        except Exception as e:
            print(f"快速页面切换失败: {e}")
            # 回退到常规方式
            self.clear_canvas()
            self.show_video_preview()

    def fast_display_cached_page(self, videos_to_display):
        """快速显示已缓存的页面内容"""
        try:
            # 更新页码显示
            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page
            self.page_label.config(text=f"{self.current_page}/{total_pages}")

            # 清理现有内容
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            row, col = 0, 0
            self.thumbnail_labels = []

            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)

                # 检查文件是否存在
                if not os.path.exists(video_path):
                    continue

                # 创建框架
                frame = tk.Frame(self.content_frame)
                frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")
                frame.grid_rowconfigure(0, weight=1)
                frame.grid_rowconfigure(1, weight=0)
                frame.grid_columnconfigure(0, weight=1)

                # 直接使用缓存的缩略图（已确认存在）
                thumbnail = self.thumbnail_cache[video_path]
                thumbnail_image = ImageTk.PhotoImage(thumbnail)

                # 创建缩略图标签
                label = tk.Label(frame, image=thumbnail_image)
                label.image = thumbnail_image  # 保持引用
                label.grid(row=0, column=0, sticky="nsew")

                # 绑定事件
                label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))
                label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 绑定预览事件
                def make_preview_handler(v_path, v_img, v_label):
                    def handler(event):
                        if v_label.winfo_exists():
                            self.show_preview(event, v_path, v_img)
                    return handler

                preview_handler = make_preview_handler(video_path, thumbnail_image, label)
                label.bind("<Enter>", preview_handler)

                # 创建信息框架
                info_frame = tk.Frame(frame)
                info_frame.grid(row=1, column=0, sticky="ew")

                # 文件名标签
                name_label = tk.Label(info_frame, text=os.path.basename(video_path), wraplength=200)
                name_label.grid(row=0, column=0, sticky="ew")
                name_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 文件信息（使用缓存）
                try:
                    size_str, resolution, duration_str, _ = self.get_video_info(video_path)
                    info_label = tk.Label(info_frame, text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
                    info_label.grid(row=1, column=0, sticky="ew")
                except Exception as e:
                    info_label = tk.Label(info_frame, text="信息获取失败")
                    info_label.grid(row=1, column=0, sticky="ew")

                # 复选框
                checkbox_var = self.checkbox_vars.get(video_path, tk.BooleanVar())
                self.checkbox_vars[video_path] = checkbox_var
                checkbox = tk.Checkbutton(info_frame, variable=checkbox_var)
                checkbox.grid(row=2, column=0)

                self.thumbnail_labels.append((label, thumbnail_image, video_path))

                # 更新网格位置
                col += 1
                if col >= self.videos_per_row:
                    col = 0
                    row += 1

            # 更新画布滚动区
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

            # 预加载相邻页面
            self.preload_adjacent_thumbnails()

        except Exception as e:
            print(f"快速显示缓存页面失败: {e}")
            # 回退到常规方式
            self.clear_canvas()
            self.show_video_preview()

    def preload_current_page_thumbnails(self):
        """预加载当前页面的缩略图"""
        try:
            videos_to_display = self.get_current_page_videos()

            def preload_worker():
                for video in videos_to_display:
                    video_path = os.path.join(self.folder_path, video)
                    if video_path not in self.thumbnail_cache and os.path.exists(video_path):
                        try:
                            # 在后台生成缩略图
                            self.generate_thumbnail(video_path)
                        except Exception:
                            break  # 如果出错就停止预加载

            # 在后台线程中预加载
            threading.Thread(target=preload_worker, daemon=True).start()

        except Exception as e:
            print(f"预加载当前页面缩略图失败: {e}")

    def get_cache_stats(self):
        """获取缓存统计信息"""
        try:
            thumbnail_count = len(self.thumbnail_cache)
            pixel_count = len(self.pixel_cache)
            video_info_count = len(self.video_info_cache)

            # 估算缓存大小
            thumbnail_size = 0
            for thumbnail in self.thumbnail_cache.values():
                try:
                    # 估算PIL图像的内存使用
                    thumbnail_size += thumbnail.size[0] * thumbnail.size[1] * 3  # RGB
                except:
                    pass

            thumbnail_size_mb = thumbnail_size / (1024 * 1024)

            return {
                'thumbnail_count': thumbnail_count,
                'pixel_count': pixel_count,
                'video_info_count': video_info_count,
                'thumbnail_size_mb': thumbnail_size_mb
            }
        except Exception as e:
            print(f"获取缓存统计失败: {e}")
            return None

    def force_refresh_current_page(self):
        """强制刷新当前页面预览图"""
        try:
            if not self.folder_path:
                return

            # 询问用户是否要清理缓存重新生成缩略图
            from tkinter import messagebox
            result = messagebox.askyesnocancel(
                "刷新选项",
                "是否清理缓存重新生成缩略图？\n\n"
                "是：清理缓存，重新生成所有缩略图（较慢但最新）\n"
                "否：保留缓存，快速刷新布局\n"
                "取消：取消刷新操作"
            )

            if result is None:  # 用户点击取消
                return
            elif result:  # 用户点击是 - 清理缓存
                current_videos = self.get_current_page_videos()
                cleared_count = 0
                for video in current_videos:
                    video_path = os.path.join(self.folder_path, video)
                    if video_path in self.thumbnail_cache:
                        del self.thumbnail_cache[video_path]
                        cleared_count += 1
                    if video_path in self.pixel_cache:
                        del self.pixel_cache[video_path]



            # 使用智能重新加载方法刷新当前页面
            self.complete_reload_after_delete()

        except Exception as e:
            print(f"强制刷新失败: {e}")

    def get_current_page_videos(self):
        """获取当前页面显示的视频列表"""
        if not hasattr(self, 'video_files') or not self.video_files:
            return []

        start_index = (self.current_page - 1) * self.videos_per_page
        end_index = start_index + self.videos_per_page
        return self.video_files[start_index:end_index]

    def clear_canvas(self):
        """清理画布内容但保留缓存"""
        # 只清理UI组件，保留所有缓存以提高性能
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 清理缩略图标签列表
        self.thumbnail_labels.clear()

    def clear_all_caches(self):
        """清理所有缓存 - 仅在必要时调用"""
        # 清理缩略图缓存
        self.thumbnail_cache.clear()

        # 清理像素缓存
        self.pixel_cache.clear()

        # 清理视频信息缓存
        self.video_info_cache.clear()

        # 重置文件夹名显示
        self.folder_label.config(text="当前文件夹: 未选择")

    def delete_selected_videos(self):
        """彻底重写的删除操作 - 确保页面显示完全正常"""
        selected_videos = [path for path, var in self.checkbox_vars.items() if var.get()]

        if not selected_videos:
            messagebox.showinfo("提示", "请先选择要删除的视频")
            return

        # 确认删除
        if not messagebox.askyesno("删除确认", f"确定要删除选中的 {len(selected_videos)} 个视频吗？"):
            return

        try:
            # 停止所有视频预览
            self.video_player.stop_video()

            # 实际执行删除操作
            deleted_count = 0
            successfully_deleted = []
            for video_path in selected_videos:
                try:
                    if os.path.exists(video_path):
                        delete_file_with_system(video_path)
                        deleted_count += 1
                        successfully_deleted.append(video_path)
                        print(f"成功删除: {os.path.basename(video_path)}")
                except Exception as e:
                    print(f"删除文件失败: {video_path}, 错误: {e}")

            # 完全清理所有相关状态

            for video_path in successfully_deleted:
                # 清理所有缓存
                self.thumbnail_cache.pop(video_path, None)
                self.pixel_cache.pop(video_path, None)
                self.checkbox_vars.pop(video_path, None)
                if hasattr(self, 'video_info_cache'):
                    self.video_info_cache.pop(video_path, None)

            # 删除后使用最可靠的刷新方法
            if successfully_deleted:
                print("删除完成，使用完全重新加载...")
                self.complete_reload_after_delete()

            # 显示删除结果消息
            if deleted_count == len(selected_videos):
                print(f"成功删除 {deleted_count} 个视频")
            else:
                messagebox.showwarning("部分完成", f"成功删除 {deleted_count} 个视频，{len(selected_videos) - deleted_count} 个视频删除失败")

        except Exception as e:
            messagebox.showerror("错误", f"删除过程中出错: {e}")
            # 出错时也完全重新加载
            self.complete_reload_after_delete()

    def complete_reload_after_delete(self):
        """删除后的智能重新加载方法 - 保留有效缓存，只清理已删除文件的缓存"""
        try:
            # 0. 短暂延迟，确保文件系统操作完全完成
            import time
            time.sleep(0.1)

            # 1. 完全清理UI
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # 2. 清理状态变量
            self.thumbnail_labels = []

            # 3. 重新扫描文件系统，确保获取最新的文件列表
            self.video_files = self.get_video_files()

            # 额外清理：从内存列表中移除任何不存在的文件
            if self.video_files:
                existing_files = []
                for video_file in self.video_files:
                    video_path = os.path.join(self.folder_path, video_file)
                    if os.path.exists(video_path):
                        existing_files.append(video_file)

                removed_count = len(self.video_files) - len(existing_files)
                if removed_count > 0:
                    self.video_files = existing_files

            # 4. 智能清理缓存 - 只清理不存在文件的缓存，保留有效缓存
            self.smart_cleanup_cache()

            # 5. 检查是否有视频文件
            if not self.video_files:
                self.page_label.config(text="0/0")
                no_video_label = tk.Label(self.content_frame, text="没有视频文件",
                                        font=("Arial", 16), fg='gray')
                no_video_label.pack(pady=50)
                return

            # 5. 重新计算页码
            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page

            # 6. 调整当前页码
            if self.current_page > total_pages:
                self.current_page = max(1, total_pages)

            # 7. 更新页码显示
            self.page_label.config(text=f"{self.current_page}/{total_pages}")

            # 8. 获取当前页要显示的视频
            start_index = (self.current_page - 1) * self.videos_per_page
            end_index = start_index + self.videos_per_page
            videos_to_display = self.video_files[start_index:end_index]

            print(f"当前页显示 {len(videos_to_display)} 个视频")

            # 9. 使用完全同步的方式生成缩略图
            self.fully_sync_generate_thumbnails(videos_to_display)

            # 10. 更新画布
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

            print("智能重新加载完成")

        except Exception as e:
            print(f"智能重新加载失败: {e}")
            # 最后的回退方案
            error_label = tk.Label(self.content_frame, text=f"加载失败: {e}",
                                 font=("Arial", 12), fg='red')
            error_label.pack(pady=20)

    def smart_cleanup_cache(self):
        """智能清理缓存 - 只清理不存在文件的缓存，保留有效缓存"""
        try:
            # 获取当前存在的所有视频文件路径
            existing_paths = set()
            if self.folder_path and self.video_files:
                for video_file in self.video_files:
                    video_path = os.path.join(self.folder_path, video_file)
                    if os.path.exists(video_path):
                        existing_paths.add(video_path)

            # 清理缩略图缓存中不存在的文件
            thumbnail_keys_to_remove = []
            for path in self.thumbnail_cache.keys():
                if path not in existing_paths:
                    thumbnail_keys_to_remove.append(path)

            for path in thumbnail_keys_to_remove:
                del self.thumbnail_cache[path]

            # 清理像素缓存中不存在的文件
            pixel_keys_to_remove = []
            for path in self.pixel_cache.keys():
                if path not in existing_paths:
                    pixel_keys_to_remove.append(path)

            for path in pixel_keys_to_remove:
                del self.pixel_cache[path]

            # 清理复选框状态中不存在的文件
            checkbox_keys_to_remove = []
            for path in self.checkbox_vars.keys():
                if path not in existing_paths:
                    checkbox_keys_to_remove.append(path)

            for path in checkbox_keys_to_remove:
                del self.checkbox_vars[path]

            # 清理视频信息缓存中不存在的文件
            if hasattr(self, 'video_info_cache'):
                info_keys_to_remove = []
                for path in self.video_info_cache.keys():
                    if path not in existing_paths:
                        info_keys_to_remove.append(path)

                for path in info_keys_to_remove:
                    del self.video_info_cache[path]

        except Exception as e:
            print(f"缓存清理失败: {e}")

    def reliable_generate_thumbnails(self, videos_to_display):
        """最可靠的缩略图生成方法 - 绝对不会出现加载中状态"""
        try:


            row, col = 0, 0
            self.thumbnail_labels = []

            for i, video in enumerate(videos_to_display):
                video_path = os.path.join(self.folder_path, video)

                print(f"处理 {i+1}/{len(videos_to_display)}: {os.path.basename(video_path)}")

                # 创建框架
                frame = tk.Frame(self.content_frame)
                frame.grid(row=row, column=col, padx=10, pady=10)

                # 生成缩略图内容
                thumbnail_created = False

                if os.path.exists(video_path):
                    # 尝试生成缩略图
                    try:
                        # 检查缓存
                        thumbnail = None
                        if video_path in self.thumbnail_cache:
                            try:
                                thumbnail = self.thumbnail_cache[video_path]
                            except:
                                del self.thumbnail_cache[video_path]

                        # 生成新缩略图
                        if thumbnail is None:
                            thumbnail = self.generate_thumbnail(video_path)

                        # 创建缩略图显示
                        if thumbnail is not None:
                            thumbnail_image = ImageTk.PhotoImage(thumbnail)
                            label = tk.Label(frame, image=thumbnail_image, compound=tk.TOP)
                            label.pack()

                            # 绑定事件
                            label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))
                            label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                            # 绑定预览事件
                            def make_preview_handler(v_path, v_img, v_label):
                                def handler(event):
                                    if v_label.winfo_exists():
                                        self.show_preview(event, v_path, v_img)
                                return handler

                            preview_handler = make_preview_handler(video_path, thumbnail_image, label)
                            label.bind("<Enter>", preview_handler)

                            self.thumbnail_labels.append((label, thumbnail_image, video_path))
                            thumbnail_created = True

                    except Exception as e:
                        print(f"缩略图生成异常: {e}")

                # 如果缩略图创建失败，显示错误信息
                if not thumbnail_created:
                    if os.path.exists(video_path):
                        error_text = "无法生成\n预览图"
                        bg_color = 'lightcoral'
                    else:
                        error_text = "文件不存在"
                        bg_color = 'lightgray'

                    error_label = tk.Label(frame, text=error_text, width=50, height=20,
                                         bg=bg_color, fg='white')
                    error_label.pack()
                    error_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 创建信息显示
                info_frame = tk.Frame(frame)
                info_frame.pack(fill=tk.X, padx=5)

                # 文件名
                name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                    wraplength=200)
                name_label.pack()
                name_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 文件信息
                if os.path.exists(video_path):
                    try:
                        size_str, resolution, duration_str, _ = self.get_video_info(video_path)
                        info_label = tk.Label(info_frame,
                                            text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
                        info_label.pack()
                    except:
                        info_label = tk.Label(info_frame, text="信息获取失败")
                        info_label.pack()
                else:
                    info_label = tk.Label(info_frame, text="文件不存在")
                    info_label.pack()

                # 复选框
                if video_path not in self.checkbox_vars:
                    self.checkbox_vars[video_path] = tk.BooleanVar()
                checkbox = tk.Checkbutton(frame, variable=self.checkbox_vars[video_path])
                checkbox.pack()

                # 更新位置
                col += 1
                if col >= self.videos_per_row:
                    col = 0
                    row += 1



        except Exception as e:
            print(f"可靠缩略图生成失败: {e}")

    def batch_cleanup_deleted_files(self, deleted_files):
        """批量清理已删除文件的缓存和状态"""
        try:
            # 批量清理缓存
            for video_path in deleted_files:
                # 清理缩略图缓存
                self.thumbnail_cache.pop(video_path, None)
                # 清理像素缓存
                self.pixel_cache.pop(video_path, None)
                # 清理复选框状态
                self.checkbox_vars.pop(video_path, None)
                # 清理视频信息缓存
                if hasattr(self, 'video_info_cache'):
                    self.video_info_cache.pop(video_path, None)


        except Exception as e:
            print(f"批量清理缓存时出错: {e}")

    def batch_cleanup_moved_files(self, moved_files):
        """批量清理已移动文件的缓存和状态"""
        try:
            # 批量清理缓存
            for video_path in moved_files:
                # 清理缩略图缓存
                self.thumbnail_cache.pop(video_path, None)
                # 清理像素缓存
                self.pixel_cache.pop(video_path, None)
                # 清理复选框状态
                self.checkbox_vars.pop(video_path, None)
                # 清理视频信息缓存
                if hasattr(self, 'video_info_cache'):
                    self.video_info_cache.pop(video_path, None)


        except Exception as e:
            print(f"批量清理移动文件缓存时出错: {e}")

    def ultra_fast_refresh_after_move(self, moved_files):
        """移动文件后的超快速刷新"""
        try:


            # 从内存中的video_files列表直接移除已移动的文件
            moved_rel_paths = set()
            for video_path in moved_files:
                try:
                    rel_path = os.path.relpath(video_path, self.folder_path)
                    moved_rel_paths.add(rel_path)
                except ValueError:
                    continue

            # 快速过滤
            original_count = len(self.video_files)
            self.video_files = [f for f in self.video_files if f not in moved_rel_paths]
            removed_count = original_count - len(self.video_files)

            print(f"从内存列表中移除了 {removed_count} 个移动的文件")

            # 检查当前页是否还有内容
            if not self.video_files:
                for widget in self.content_frame.winfo_children():
                    widget.destroy()
                self.page_label.config(text="0/0")
                return

            # 重新计算页码并智能刷新
            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page
            if self.current_page > total_pages:
                self.current_page = max(1, total_pages)

            self.smart_refresh_current_page()
            print("移动后超快速刷新完成")

        except Exception as e:
            print(f"移动后超快速刷新失败: {e}")
            self.fast_refresh_preview()

    def ultra_fast_refresh_after_delete(self, deleted_files):
        """超快速刷新 - 直接从内存列表中移除已删除文件，避免文件系统扫描"""
        try:
            print("开始超快速刷新...")

            # 从内存中的video_files列表直接移除已删除的文件
            deleted_rel_paths = set()
            for video_path in deleted_files:
                try:
                    rel_path = os.path.relpath(video_path, self.folder_path)
                    deleted_rel_paths.add(rel_path)
                except ValueError:
                    # 处理路径不在当前文件夹下的情况
                    continue

            # 使用列表推导式快速过滤
            original_count = len(self.video_files)
            self.video_files = [f for f in self.video_files if f not in deleted_rel_paths]
            removed_count = original_count - len(self.video_files)

            print(f"从内存列表中移除了 {removed_count} 个文件")

            # 检查当前页是否还有内容
            if not self.video_files:
                # 如果没有视频了，清空显示
                for widget in self.content_frame.winfo_children():
                    widget.destroy()
                self.page_label.config(text="0/0")
                return

            # 重新计算页码
            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page

            # 如果当前页超出范围，调整到最后一页
            if self.current_page > total_pages:
                self.current_page = max(1, total_pages)

            # 使用简单可靠的刷新方法
            self.simple_refresh_after_delete()

            print("超快速刷新完成")

        except Exception as e:
            print(f"超快速刷新失败: {e}")
            # 如果超快速刷新失败，回退到完整刷新
            self.show_video_preview(force_rescan=True)

    def simple_refresh_after_delete(self):
        """删除后的简单可靠刷新方法"""
        try:
            print("开始简单刷新...")

            # 清理现有UI内容
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # 检查是否有视频文件
            if not self.video_files:
                self.page_label.config(text="0/0")
                return

            # 更新页码显示
            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page
            self.page_label.config(text=f"{self.current_page}/{total_pages}")

            # 获取当前页要显示的视频
            start_index = (self.current_page - 1) * self.videos_per_page
            end_index = start_index + self.videos_per_page
            videos_to_display = self.video_files[start_index:end_index]

            # 使用最简单的同步方式生成缩略图
            row, col = 0, 0
            self.thumbnail_labels = []

            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)
                if not os.path.exists(video_path):
                    print(f"跳过不存在的文件: {video_path}")
                    continue

                try:
                    # 创建框架
                    frame = tk.Frame(self.content_frame)
                    frame.grid(row=row, column=col, padx=10, pady=10)

                    # 生成或获取缓存的缩略图
                    thumbnail = None
                    if video_path in self.thumbnail_cache:
                        try:
                            thumbnail = self.thumbnail_cache[video_path]
                        except:
                            del self.thumbnail_cache[video_path]

                    if thumbnail is None:
                        thumbnail = self.generate_thumbnail(video_path)

                    if thumbnail is not None:
                        # 成功获取缩略图
                        thumbnail_image = ImageTk.PhotoImage(thumbnail)
                        label = tk.Label(frame, image=thumbnail_image, compound=tk.TOP)
                        label.pack()

                        # 绑定事件
                        label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))
                        label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                        # 绑定预览事件
                        def make_preview_handler(v_path, v_img, v_label):
                            def handler(event):
                                if v_label.winfo_exists():
                                    self.show_preview(event, v_path, v_img)
                            return handler

                        preview_handler = make_preview_handler(video_path, thumbnail_image, label)
                        label.bind("<Enter>", preview_handler)

                        self.thumbnail_labels.append((label, thumbnail_image, video_path))
                    else:
                        # 缩略图生成失败
                        error_label = tk.Label(frame, text="无法加载\n预览图",
                                             width=50, height=20, bg='lightcoral', fg='white')
                        error_label.pack()
                        error_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                    # 显示视频信息
                    info_frame = tk.Frame(frame)
                    info_frame.pack(fill=tk.X, padx=5)

                    name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                        wraplength=200)
                    name_label.pack()
                    name_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                    # 获取并显示视频信息
                    try:
                        size_str, resolution, duration_str, _ = self.get_video_info(video_path)
                        info_label = tk.Label(info_frame,
                                            text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
                        info_label.pack()
                    except:
                        info_label = tk.Label(info_frame, text="信息获取失败")
                        info_label.pack()

                    # 创建复选框
                    if video_path not in self.checkbox_vars:
                        self.checkbox_vars[video_path] = tk.BooleanVar()
                    checkbox = tk.Checkbutton(frame, variable=self.checkbox_vars[video_path])
                    checkbox.pack()

                    # 更新位置
                    col += 1
                    if col >= self.videos_per_row:
                        col = 0
                        row += 1

                except Exception as e:
                    print(f"处理视频 {video} 时出错: {e}")
                    continue

            # 更新画布滚动区
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

            print("简单刷新完成")

        except Exception as e:
            print(f"简单刷新失败: {e}")
            # 最后的回退方案
            self.show_video_preview(force_rescan=True)

    def sync_grid_generate_thumbnails(self, videos_to_display):
        """同步网格对齐的缩略图生成方法 - 确保每个位置都有内容，不会出现空隙"""
        try:


            row, col = 0, 0
            self.thumbnail_labels = []

            for i, video in enumerate(videos_to_display):
                video_path = os.path.join(self.folder_path, video)

                print(f"处理第 {i+1}/{len(videos_to_display)} 个视频: {os.path.basename(video_path)}")

                # 创建框架 - 无论如何都要创建，确保网格对齐
                frame = tk.Frame(self.content_frame)
                frame.grid(row=row, column=col, padx=10, pady=10)

                # 检查文件是否存在
                if not os.path.exists(video_path):
                    print(f"文件不存在: {video_path}")
                    # 显示文件不存在的提示
                    error_label = tk.Label(frame, text="文件不存在", width=50, height=20,
                                         bg='lightgray', fg='red')
                    error_label.pack()
                else:
                    # 尝试生成缩略图
                    thumbnail = None
                    thumbnail_from_cache = False

                    # 检查缓存
                    if video_path in self.thumbnail_cache:
                        try:
                            thumbnail = self.thumbnail_cache[video_path]
                            thumbnail_from_cache = True
                        except Exception as e:
                            # 清理损坏的缓存
                            del self.thumbnail_cache[video_path]

                    # 如果没有缓存，生成新的缩略图
                    if thumbnail is None:
                        thumbnail = self.generate_thumbnail(video_path)

                    # 创建显示内容
                    if thumbnail is not None:
                        try:
                            # 成功获取缩略图
                            thumbnail_image = ImageTk.PhotoImage(thumbnail)
                            label = tk.Label(frame, image=thumbnail_image, compound=tk.TOP)
                            label.pack()

                            # 绑定事件
                            label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))
                            label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                            # 绑定预览事件
                            def make_preview_handler(v_path, v_img, v_label):
                                def handler(event):
                                    if v_label.winfo_exists():
                                        self.show_preview(event, v_path, v_img)
                                return handler

                            preview_handler = make_preview_handler(video_path, thumbnail_image, label)
                            label.bind("<Enter>", preview_handler)

                            self.thumbnail_labels.append((label, thumbnail_image, video_path))

                        except Exception as e:
                            print(f"创建缩略图标签失败: {e}")
                            # 显示错误信息
                            error_label = tk.Label(frame, text="缩略图\n创建失败", width=50, height=20,
                                                 bg='lightcoral', fg='white')
                            error_label.pack()
                            error_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))
                    else:
                        # 缩略图生成失败
                        error_label = tk.Label(frame, text="无法生成\n预览图", width=50, height=20,
                                             bg='lightcoral', fg='white')
                        error_label.pack()
                        error_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 创建信息显示 - 无论缩略图是否成功都要显示
                info_frame = tk.Frame(frame)
                info_frame.pack(fill=tk.X, padx=5)

                # 显示文件名
                name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                    wraplength=200)
                name_label.pack()
                name_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 显示文件信息
                if os.path.exists(video_path):
                    try:
                        size_str, resolution, duration_str, _ = self.get_video_info(video_path)
                        info_label = tk.Label(info_frame,
                                            text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
                        info_label.pack()
                    except Exception as e:
                        print(f"获取视频信息失败: {e}")
                        info_label = tk.Label(info_frame, text="信息获取失败")
                        info_label.pack()
                else:
                    info_label = tk.Label(info_frame, text="文件不存在")
                    info_label.pack()

                # 创建复选框 - 无论如何都要创建
                if video_path not in self.checkbox_vars:
                    self.checkbox_vars[video_path] = tk.BooleanVar()
                checkbox = tk.Checkbutton(frame, variable=self.checkbox_vars[video_path])
                checkbox.pack()

                # 更新网格位置
                col += 1
                if col >= self.videos_per_row:
                    col = 0
                    row += 1

            # 更新画布滚动区
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))



        except Exception as e:
            print(f"同步缩略图生成失败: {e}")
            # 如果还是失败，显示错误信息
            error_label = tk.Label(self.content_frame, text=f"缩略图生成失败: {e}",
                                 font=("Arial", 12), fg='red')
            error_label.pack(pady=20)

    def on_frame_configure(self, event=None):
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def _on_mousewheel(self, event):
        # Windows 鼠标滚轮事件
        if event.num == 5 or event.delta < 0:
            self.canvas.yview_scroll(1, "units")
        # Linux 鼠标滚轮事件
        elif event.num == 4 or event.delta > 0:
            self.canvas.yview_scroll(-1, "units")

    def open_fullscreen_player(self, video_path):
        """打开全屏播放器"""
        try:
            # 停止预览播放
            self.video_player.stop_video()

            # 检查文件夹路径是否为空
            if not self.folder_path:
                messagebox.showerror("错误", "文件夹路径未设置")
                return

            # 获取完整的视频列表
            video_list = [os.path.join(self.folder_path, f) for f in self.video_files]

            # 打开全屏播放窗口
            player = FullscreenPlayer(video_path,
                                    video_list,
                                    self.refresh_after_delete,  # 使用专门的刷新方法
                                    self.move_history,
                                    main_app=self)  # 直接传递self作为main_app

        except Exception as e:
            print(f"打开全屏播放器时出错: {e}")
            messagebox.showerror("错误", f"打开全屏播放器时出错: {e}")

    def fast_refresh_preview(self):
        """快速刷新预览，避免重新扫描文件系统"""
        try:
            print("开始快速刷新预览...")

            # 清理现有UI内容
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # 重新显示当前页的视频（使用已有的video_files列表）
            if not self.video_files:
                self.page_label.config(text="0/0")
                return

            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page

            # 检查当前页是否超出范围
            if self.current_page > total_pages:
                self.current_page = max(1, total_pages)

            # 更新页码显示
            self.page_label.config(text=f"{self.current_page}/{total_pages}")

            start_index = (self.current_page - 1) * self.videos_per_page
            end_index = start_index + self.videos_per_page
            videos_to_display = self.video_files[start_index:end_index]

            # 使用完全同步的缩略图生成
            self.fully_sync_generate_thumbnails(videos_to_display)

            print("快速刷新完成")

        except Exception as e:
            print(f"快速刷新失败: {e}")
            # 如果快速刷新失败，回退到完整刷新
            self.show_video_preview(force_rescan=True)

    def smart_refresh_current_page(self):
        """智能刷新当前页面 - 优先使用缓存的缩略图"""
        try:
            print("开始智能刷新当前页面...")

            # 清理现有UI内容
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            # 检查是否有视频文件
            if not self.video_files:
                self.page_label.config(text="0/0")
                return

            # 更新页码显示
            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page
            self.page_label.config(text=f"{self.current_page}/{total_pages}")

            # 获取当前页要显示的视频
            start_index = (self.current_page - 1) * self.videos_per_page
            end_index = start_index + self.videos_per_page
            videos_to_display = self.video_files[start_index:end_index]

            # 使用稳定的缩略图生成方法
            self.stable_generate_thumbnails(videos_to_display)

            # 管理缓存大小
            self.manage_thumbnail_cache()

            # 预加载相邻页面的缩略图
            self.preload_adjacent_thumbnails()

            print("智能刷新完成")

        except Exception as e:
            print(f"智能刷新失败: {e}")
            # 回退到稳定生成方法
            self.stable_generate_thumbnails(videos_to_display if 'videos_to_display' in locals() else [])

    def optimized_generate_thumbnails(self, videos_to_display):
        """优化的缩略图生成 - 优先使用缓存"""
        try:
            row, col = 0, 0
            self.thumbnail_labels = []
            cached_count = 0
            need_generate = []

            # 第一阶段：快速显示已缓存的缩略图
            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)
                if not os.path.exists(video_path):
                    continue

                # 创建框架
                frame = tk.Frame(self.content_frame)
                frame.grid(row=row, column=col, padx=10, pady=10)

                # 检查是否有缓存的缩略图
                if video_path in self.thumbnail_cache:
                    # 使用缓存的缩略图
                    try:
                        thumbnail = self.thumbnail_cache[video_path]
                        thumbnail_image = ImageTk.PhotoImage(thumbnail)

                        # 创建标签显示缩略图
                        label = tk.Label(frame, image=thumbnail_image, compound=tk.TOP)
                        label.pack()

                        # 绑定事件
                        self.bind_thumbnail_events(label, video_path, video, thumbnail_image)

                        # 显示视频信息
                        self.create_video_info_display(frame, video, video_path)

                        cached_count += 1

                    except Exception as e:
                        print(f"使用缓存缩略图失败: {e}")
                        # 创建占位符，添加到需要重新生成的列表
                        placeholder_label = tk.Label(frame, text="加载中...", width=50, height=20, bg='lightgray')
                        placeholder_label.pack()
                        need_generate.append((video, video_path, frame, row, col, placeholder_label))
                else:
                    # 创建占位符，添加到需要生成的列表
                    placeholder_label = tk.Label(frame, text="加载中...", width=50, height=20, bg='lightgray')
                    placeholder_label.pack()
                    need_generate.append((video, video_path, frame, row, col, placeholder_label))

                col += 1
                if col >= self.videos_per_row:
                    col = 0
                    row += 1



            # 第二阶段：异步生成未缓存的缩略图
            if need_generate:
                self.async_generate_missing_thumbnails(need_generate)

            # 更新画布滚动区
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        except Exception as e:
            print(f"优化缩略图生成失败: {e}")
            # 回退到完全同步方法
            self.fully_sync_generate_thumbnails(videos_to_display)

    def bind_thumbnail_events(self, label, video_path, video, thumbnail_image):
        """绑定缩略图标签的事件"""
        try:
            # 绑定双击事件
            label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))

            # 绑定右键菜单
            label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

            # 绑定预览事件
            def make_preview_handler(v_path, v_img, v_label):
                def handler(event):
                    if v_label.winfo_exists():
                        self.show_preview(event, v_path, v_img)
                return handler

            preview_handler = make_preview_handler(video_path, thumbnail_image, label)
            label.bind("<Enter>", preview_handler)

            # 保存到缩略图标签列表
            self.thumbnail_labels.append((label, thumbnail_image, video_path))

        except Exception as e:
            print(f"绑定缩略图事件失败: {e}")

    def create_video_info_display(self, frame, video, video_path):
        """创建视频信息显示"""
        try:
            # 创建信息框架
            info_frame = tk.Frame(frame)
            info_frame.pack(fill=tk.X, padx=5)

            # 显示文件名（限制长度）
            name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                wraplength=200)
            name_label.pack()

            # 绑定右键菜单到名称标签
            name_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

            # 获取并显示视频信息
            size_str, resolution, duration_str, _ = self.get_video_info(video_path)
            info_label = tk.Label(info_frame,
                                text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
            info_label.pack()

            # 创建复选框
            if video_path not in self.checkbox_vars:
                self.checkbox_vars[video_path] = tk.BooleanVar()
            checkbox = tk.Checkbutton(frame, variable=self.checkbox_vars[video_path])
            checkbox.pack()

        except Exception as e:
            print(f"创建视频信息显示失败: {e}")

    def async_generate_missing_thumbnails(self, need_generate):
        """异步生成缺失的缩略图"""
        def generate_thumbnails_worker():
            for item in need_generate:
                try:
                    # 现在所有项目都有占位符
                    video, video_path, frame, _, _, placeholder_label = item

                    # 检查占位符是否还存在
                    if not placeholder_label.winfo_exists():
                        continue

                    # 生成缩略图
                    thumbnail = self.generate_thumbnail(video_path)

                    # 在主线程中更新UI
                    def update_ui():
                        try:
                            if placeholder_label.winfo_exists():
                                # 替换占位符
                                placeholder_label.destroy()

                                if thumbnail is not None:
                                    # 成功生成缩略图
                                    thumbnail_image = ImageTk.PhotoImage(thumbnail)
                                    label = tk.Label(frame, image=thumbnail_image, compound=tk.TOP)
                                    label.pack()

                                    # 绑定事件
                                    self.bind_thumbnail_events(label, video_path, video, thumbnail_image)

                                    # 创建信息显示
                                    self.create_video_info_display(frame, video, video_path)
                                else:
                                    # 缩略图生成失败，显示错误信息
                                    error_label = tk.Label(frame, text="无法加载\n预览图",
                                                         width=50, height=20, bg='lightcoral', fg='white')
                                    error_label.pack()

                                    # 仍然绑定右键菜单
                                    error_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                                    # 创建基本信息显示
                                    info_frame = tk.Frame(frame)
                                    info_frame.pack(fill=tk.X, padx=5)

                                    name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                                        wraplength=200)
                                    name_label.pack()

                                    try:
                                        file_size = os.path.getsize(video_path)
                                        size_str = f"{file_size/(1024*1024):.1f}MB" if file_size >= 1024*1024 else f"{file_size/1024:.1f}KB"
                                        info_label = tk.Label(info_frame, text=f"大小: {size_str}\n状态: 无法预览")
                                        info_label.pack()
                                    except:
                                        info_label = tk.Label(info_frame, text="状态: 无法预览")
                                        info_label.pack()

                                    # 创建复选框
                                    if video_path not in self.checkbox_vars:
                                        self.checkbox_vars[video_path] = tk.BooleanVar()
                                    checkbox = tk.Checkbutton(frame, variable=self.checkbox_vars[video_path])
                                    checkbox.pack()

                        except Exception as e:
                            print(f"更新缺失缩略图UI失败: {e}")

                    # 在主线程中执行UI更新
                    self.master.after(0, update_ui)

                except Exception as e:
                    print(f"异步生成缺失缩略图失败: {e}")

        # 在后台线程中生成
        threading.Thread(target=generate_thumbnails_worker, daemon=True).start()

    def stable_generate_thumbnails(self, videos_to_display):
        """稳定的缩略图生成方法 - 修复网格布局问题"""
        try:
            row, col = 0, 0
            self.thumbnail_labels = []
            successfully_displayed = 0

            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)
                if not os.path.exists(video_path):
                    print(f"跳过不存在的文件: {video_path}")
                    continue

                try:
                    # 生成或获取缓存的缩略图
                    thumbnail = None
                    thumbnail_image = None

                    # 检查是否有缓存的缩略图
                    if video_path in self.thumbnail_cache:
                        try:
                            thumbnail = self.thumbnail_cache[video_path]
                            thumbnail_image = ImageTk.PhotoImage(thumbnail)
                        except Exception as e:
                            # 清理损坏的缓存
                            del self.thumbnail_cache[video_path]
                            thumbnail = None

                    # 如果没有缓存或缓存损坏，生成新的缩略图
                    if thumbnail is None:
                        thumbnail = self.generate_thumbnail(video_path)
                        if thumbnail is None:
                            continue
                        thumbnail_image = ImageTk.PhotoImage(thumbnail)

                    # 创建框架 - 只有在成功获取缩略图后才创建
                    frame = tk.Frame(self.content_frame)
                    frame.grid(row=row, column=col, padx=10, pady=10)

                    # 创建标签显示缩略图
                    label = tk.Label(frame, image=thumbnail_image, compound=tk.TOP)
                    label.pack()

                    # 绑定事件
                    self.bind_thumbnail_events(label, video_path, video, thumbnail_image)

                    # 显示视频信息
                    self.create_video_info_display(frame, video, video_path)

                    # 只有成功创建了UI元素才更新位置计数
                    successfully_displayed += 1
                    col += 1
                    if col >= self.videos_per_row:
                        col = 0
                        row += 1

                except Exception as e:
                    print(f"处理视频 {video} 时出错: {e}")
                    # 不更新位置计数，继续处理下一个视频
                    continue

            # 更新画布滚动区
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))



        except Exception as e:
            print(f"稳定缩略图生成失败: {e}")
            # 如果还是失败，使用完全同步方法
            self.fully_sync_generate_thumbnails(videos_to_display)

    def refresh_after_delete(self):
        """播放界面删除文件后的专门刷新方法 - 统一使用完全重新加载"""
        try:
            self.complete_reload_after_delete()
        except Exception as e:
            print(f"播放界面刷新失败: {e}")

    def similarity_sort(self, files):
        """基于文件名相似度的排序"""
        if not files:  # 如果文件列表为空，直接返回
            return []

        def clean_filename(name):
            # 移除扩展名和常见分隔符
            name = os.path.splitext(name)[0]
            # 移除数字和特殊字符，保留主要文字内容
            return ''.join(c for c in name if not c.isdigit() and c not in '.-_[](){}')

        def calculate_similarity(str1, str2):
            # 使用最长公共子序列(LCS)计算相似度
            str1 = clean_filename(str1.lower())
            str2 = clean_filename(str2.lower())

            if not str1 or not str2:
                return 0

            matrix = [[0] * (len(str2) + 1) for _ in range(len(str1) + 1)]
            for i in range(1, len(str1) + 1):
                for j in range(1, len(str2) + 1):
                    if str1[i-1] == str2[j-1]:
                        matrix[i][j] = matrix[i-1][j-1] + 1
                    else:
                        matrix[i][j] = max(matrix[i-1][j], matrix[i][j-1])

            lcs_length = matrix[-1][-1]
            max_length = max(len(str1), len(str2))
            return lcs_length / max_length if max_length > 0 else 0

        # 构建相似度矩阵
        n = len(files)
        similarity_matrix = [[0] * n for _ in range(n)]

        for i in range(n):
            for j in range(i + 1, n):
                similarity = calculate_similarity(files[i], files[j])
                similarity_matrix[i][j] = similarity
                similarity_matrix[j][i] = similarity

        # 使用聚类方式重新排序
        result = []
        used = set()
        current = 0  # 从第一个文件开始

        while len(used) < n:
            if current not in used:
                result.append(files[current])
                used.add(current)

                # 找到与当前文件最相似的未使用文件
                max_similarity = -1
                next_file = None

                for i in range(n):
                    if i not in used and similarity_matrix[current][i] > max_similarity:
                        max_similarity = similarity_matrix[current][i]
                        next_file = i

                # 修改这部分逻辑，避免空集合
                unused_files = set(range(n)) - used
                if next_file is not None:
                    current = next_file
                elif unused_files:  # 确保还有未使用的文件
                    current = min(unused_files)
                else:
                    break  # 如果没有未使用的文件，退出循环
            else:
                current = min(set(range(n)) - used)

        return result if not self.sort_reverse.get() else result[::-1]

    def show_context_menu(self, event, video):
        """显示右键菜单"""
        self.current_video = video  # 保存当前选中的视频文件名
        self.context_menu.post(event.x_root, event.y_root)

    def show_folder_context_menu(self, event):
        """显示文件夹标签的右键菜单"""
        if not self.folder_path:
            return  # 如果没有打开文件夹，不显示菜单
        self.folder_context_menu.post(event.x_root, event.y_root)

    def copy_filename(self):
        """复制文件名到剪贴板"""
        if hasattr(self, 'current_video'):
            filename = os.path.splitext(self.current_video)[0]  # 不包含扩展名
            self.master.clipboard_clear()
            self.master.clipboard_append(filename)
            messagebox.showinfo("提示", "文件名已复制到剪贴板")

    def rename_file(self):
        """重命名文件"""
        if not hasattr(self, 'current_video'):
            return

        old_path = os.path.join(self.folder_path, self.current_video)
        if not os.path.exists(old_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 创建重命名对话框
        dialog = tk.Toplevel(self.master)
        dialog.title("重命名")
        dialog.geometry("400x150")
        dialog.transient(self.master)  # 设置为主窗口的临时窗口

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.master.winfo_rootx() + self.master.winfo_width()/2 - 200,
            self.master.winfo_rooty() + self.master.winfo_height()/2 - 75
        ))

        # 添加说明标签
        tk.Label(dialog, text="请输入新的文件名：").pack(pady=10)

        # 文件名输入框
        filename = os.path.splitext(self.current_video)[0]
        extension = os.path.splitext(self.current_video)[1]
        entry = tk.Entry(dialog, width=50)
        entry.insert(0, filename)
        entry.pack(padx=10, pady=5)
        entry.select_range(0, tk.END)  # 选中文本

        def do_rename():
            new_name = entry.get().strip()
            if not new_name:
                messagebox.showerror("错误", "文件名不能为空")
                return

            new_path = os.path.join(self.folder_path, new_name + extension)

            # 检查文件名是否合法
            if not all(c not in '<>:"/\\|?*' for c in new_name):
                messagebox.showerror("错误", "文件名包含非法字符")
                return

            # 检查文件是否已存在
            if os.path.exists(new_path) and new_path != old_path:
                messagebox.showerror("错误", "文件已存在")
                return

            try:
                # 停止视频预览
                self.video_player.stop_video()
                time.sleep(0.1)  # 等待资源释放

                # 重命名文件
                os.rename(old_path, new_path)

                # 更新显示
                self.show_video_preview()
                dialog.destroy()
                messagebox.showinfo("成功", "文件重命名成功")
            except Exception as e:
                messagebox.showerror("错误", f"重命名失败: {str(e)}")

        # 添加确定和取消按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="确定", command=do_rename).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

        # 绑定回车键
        entry.bind("<Return>", lambda e: do_rename())

        # 设置焦点
        entry.focus_set()

    def rename_current_folder(self):
        """重命名当前文件夹"""
        if not self.folder_path:
            messagebox.showerror("错误", "请先打开文件夹")
            return

        if not os.path.exists(self.folder_path):
            messagebox.showerror("错误", "当前文件夹不存在")
            return

        # 获取当前文件夹名和父目录
        current_folder_name = os.path.basename(self.folder_path)
        parent_dir = os.path.dirname(self.folder_path)

        # 创建重命名对话框
        dialog = tk.Toplevel(self.master)
        dialog.title("重命名文件夹")
        dialog.geometry("400x150")
        dialog.transient(self.master)

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.master.winfo_rootx() + self.master.winfo_width()/2 - 200,
            self.master.winfo_rooty() + self.master.winfo_height()/2 - 75
        ))

        # 添加说明标签
        tk.Label(dialog, text="请输入新的文件夹名：").pack(pady=10)

        # 文件夹名输入框
        entry = tk.Entry(dialog, width=50)
        entry.insert(0, current_folder_name)
        entry.pack(padx=10, pady=5)
        entry.select_range(0, tk.END)  # 选中文本

        def do_rename():
            new_name = entry.get().strip()
            if not new_name:
                messagebox.showerror("错误", "文件夹名不能为空")
                return

            if new_name == current_folder_name:
                dialog.destroy()
                return

            # 检查新文件夹名是否包含非法字符
            invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
            if any(char in new_name for char in invalid_chars):
                messagebox.showerror("错误", "文件夹名包含非法字符")
                return

            new_folder_path = os.path.join(parent_dir, new_name)

            if os.path.exists(new_folder_path):
                # 显示冲突处理对话框
                choice, final_name = self.show_folder_conflict_dialog(new_name, parent_dir)
                if choice == "cancel":
                    return
                elif choice == "merge":
                    # 执行合并操作
                    self.merge_folders(self.folder_path, new_folder_path)
                    return
                elif choice == "rename":
                    # 使用新名称
                    new_name = final_name
                    new_folder_path = os.path.join(parent_dir, new_name)
                elif choice == "auto_rename":
                    # 自动生成不冲突的名称
                    new_name = self.generate_unique_folder_name(parent_dir, new_name)
                    new_folder_path = os.path.join(parent_dir, new_name)

            try:
                # 停止视频预览
                self.video_player.stop_video()
                time.sleep(0.1)  # 等待资源释放

                # 重命名文件夹
                os.rename(self.folder_path, new_folder_path)

                # 更新当前文件夹路径
                self.folder_path = new_folder_path

                # 更新文件夹名显示
                self.folder_label.config(text=f"当前文件夹: {new_name}")

                # 保存新的文件夹路径
                self.save_last_folder(new_folder_path)

                # 清理所有缓存
                self.thumbnail_cache.clear()
                self.pixel_cache.clear()
                self.video_info_cache.clear()

                # 刷新显示
                self.show_video_preview()

                # 关闭对话框
                dialog.destroy()

                print(f"文件夹已重命名为: {new_name}")  # 不显示成功提示，根据用户偏好

            except Exception as e:
                messagebox.showerror("错误", f"重命名失败: {str(e)}")

        # 添加按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="确定", command=do_rename).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

        # 绑定回车键
        entry.bind("<Return>", lambda e: do_rename())

        # 设置焦点
        entry.focus_set()

    def show_folder_conflict_dialog(self, folder_name, parent_dir):
        """显示文件夹冲突处理对话框"""
        dialog = tk.Toplevel(self.master)
        dialog.title("文件夹冲突")
        dialog.geometry("450x300")
        dialog.transient(self.master)
        dialog.grab_set()  # 模态对话框

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.master.winfo_rootx() + self.master.winfo_width()//2 - 225,
            self.master.winfo_rooty() + self.master.winfo_height()//2 - 150
        ))

        # 用于存储用户选择的变量
        result = {"choice": "cancel", "new_name": folder_name}

        # 创建主框架
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 提示信息
        message_label = tk.Label(
            main_frame,
            text=f"文件夹 '{folder_name}' 在目标位置已存在。\n您希望如何处理？",
            justify=tk.CENTER,
            font=("Arial", 10)
        )
        message_label.pack(pady=(0, 15))

        # 重命名输入框架
        rename_frame = tk.LabelFrame(main_frame, text="重命名选项")
        rename_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(rename_frame, text="新文件夹名:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        name_var = tk.StringVar(value=folder_name)
        name_entry = tk.Entry(rename_frame, textvariable=name_var, width=50)
        name_entry.pack(fill=tk.X, padx=5, pady=(0, 5))

        # 按钮框架
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # 创建按钮处理函数
        def on_merge():
            result["choice"] = "merge"
            dialog.destroy()

        def on_rename():
            new_name = name_var.get().strip()
            if not new_name:
                messagebox.showerror("错误", "文件夹名不能为空")
                return
            if new_name == folder_name:
                messagebox.showwarning("警告", "请输入一个不同的文件夹名")
                return
            # 检查新名称是否合法
            if any(c in new_name for c in r'\/:*?"<>|'):
                messagebox.showerror("错误", "文件夹名不能包含下列字符: \\ / : * ? \" < > |")
                return
            # 检查新名称是否也冲突
            new_path = os.path.join(parent_dir, new_name)
            if os.path.exists(new_path):
                messagebox.showerror("错误", f"文件夹 {new_name} 也已存在")
                return
            result["choice"] = "rename"
            result["new_name"] = new_name
            dialog.destroy()

        def on_auto_rename():
            result["choice"] = "auto_rename"
            dialog.destroy()

        def on_cancel():
            result["choice"] = "cancel"
            dialog.destroy()

        # 第一行按钮
        top_button_frame = tk.Frame(button_frame)
        top_button_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Button(top_button_frame, text="合并文件夹", command=on_merge, width=12,
                 bg="#4CAF50", fg="white").pack(side=tk.LEFT, padx=2)
        tk.Button(top_button_frame, text="重命名", command=on_rename, width=10).pack(side=tk.LEFT, padx=2)
        tk.Button(top_button_frame, text="自动重命名", command=on_auto_rename, width=12).pack(side=tk.LEFT, padx=2)

        # 第二行按钮
        bottom_button_frame = tk.Frame(button_frame)
        bottom_button_frame.pack(fill=tk.X)

        tk.Button(bottom_button_frame, text="取消", command=on_cancel, width=10).pack(side=tk.RIGHT, padx=2)

        # 绑定回车键到重命名
        name_entry.bind('<Return>', lambda e: on_rename())
        # 绑定ESC键到取消
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 选中文件夹名
        name_entry.selection_range(0, len(folder_name))
        name_entry.focus()

        # 等待对话框关闭
        self.master.wait_window(dialog)

        return result["choice"], result["new_name"]

    def generate_unique_folder_name(self, parent_dir, folder_name):
        """生成唯一的文件夹名，避免冲突"""
        counter = 1
        while True:
            new_name = f"{folder_name}-{counter}"
            new_path = os.path.join(parent_dir, new_name)
            if not os.path.exists(new_path):
                return new_name
            counter += 1
            # 防止无限循环
            if counter > 1000:
                import time
                timestamp = int(time.time())
                return f"{folder_name}_{timestamp}"

    def merge_folders(self, source_folder, target_folder):
        """合并文件夹：将源文件夹中的所有文件移动到目标文件夹"""
        try:
            # 停止视频预览
            self.video_player.stop_video()
            time.sleep(0.1)  # 等待资源释放

            # 获取源文件夹中的所有文件
            source_files = []
            for item in os.listdir(source_folder):
                item_path = os.path.join(source_folder, item)
                if os.path.isfile(item_path):
                    source_files.append(item)

            if not source_files:
                # 如果源文件夹为空，直接删除
                os.rmdir(source_folder)
                messagebox.showinfo("合并完成", "源文件夹为空，已删除空文件夹")
                return

            # 显示合并确认对话框
            if not messagebox.askyesno("确认合并",
                f"将要把 {len(source_files)} 个文件从源文件夹合并到目标文件夹。\n\n"
                f"源文件夹: {os.path.basename(source_folder)}\n"
                f"目标文件夹: {os.path.basename(target_folder)}\n\n"
                "确定要继续吗？"):
                return

            moved_count = 0
            failed_files = []
            overwrite_all = False
            skip_all = False

            # 移动每个文件
            for filename in source_files:
                if skip_all:
                    failed_files.append(f"{filename} - 用户跳过")
                    continue

                source_file_path = os.path.join(source_folder, filename)
                target_file_path = os.path.join(target_folder, filename)

                try:
                    # 检查目标文件是否已存在
                    if os.path.exists(target_file_path):
                        if not overwrite_all:
                            choice = messagebox.askyesnocancel(
                                "文件冲突",
                                f"文件 '{filename}' 在目标文件夹中已存在。\n\n"
                                "是：覆盖此文件\n"
                                "否：跳过此文件\n"
                                "取消：停止合并操作"
                            )
                            if choice is None:  # 取消
                                break
                            elif choice is False:  # 跳过
                                failed_files.append(f"{filename} - 用户跳过")
                                continue
                            # choice is True 时继续覆盖

                    # 移动文件
                    shutil.move(source_file_path, target_file_path)
                    moved_count += 1

                except Exception as e:
                    failed_files.append(f"{filename} - {str(e)}")

            # 尝试删除源文件夹（如果为空）
            try:
                remaining_items = os.listdir(source_folder)
                if not remaining_items:
                    os.rmdir(source_folder)
                    print(f"已删除空的源文件夹: {source_folder}")
            except Exception as e:
                print(f"删除源文件夹失败: {e}")

            # 更新当前文件夹路径到目标文件夹
            self.folder_path = target_folder

            # 更新文件夹名显示
            self.folder_label.config(text=f"当前文件夹: {os.path.basename(target_folder)}")

            # 保存新的文件夹路径
            self.save_last_folder(target_folder)

            # 清理所有缓存
            self.thumbnail_cache.clear()
            self.pixel_cache.clear()
            self.video_info_cache.clear()

            # 刷新显示
            self.show_video_preview()

            # 显示结果
            if failed_files:
                messagebox.showwarning("合并完成",
                    f"合并完成！\n\n"
                    f"成功移动: {moved_count} 个文件\n"
                    f"失败/跳过: {len(failed_files)} 个文件\n\n"
                    f"失败列表:\n" + "\n".join(failed_files[:10]) +
                    ("..." if len(failed_files) > 10 else ""))
            else:
                print(f"文件夹合并完成，成功移动 {moved_count} 个文件")  # 不显示成功提示，根据用户偏好

        except Exception as e:
            messagebox.showerror("错误", f"合并文件夹失败: {str(e)}")

    def move_current_file(self):
        """移动当前右键选中的文件"""
        if not hasattr(self, 'current_video') or not self.current_video:
            return

        if not self.folder_path:
            messagebox.showerror("错误", "请先打开文件夹")
            return

        video_path = os.path.join(self.folder_path, self.current_video)

        if not os.path.exists(video_path):
            messagebox.showerror("错误", "文件不存在")
            return

        dialog = tk.Toplevel(self.master)
        dialog.title(f"移动文件: {self.current_video}")
        dialog.geometry("400x300")
        dialog.transient(self.master)

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.master.winfo_rootx() + self.master.winfo_width()/2 - 200,
            self.master.winfo_rooty() + self.master.winfo_height()/2 - 150
        ))

        # 创建历史记录列表框
        history_frame = tk.Frame(dialog)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        tk.Label(history_frame, text="最近使用的目标文件夹:").pack(anchor=tk.W)

        listbox = tk.Listbox(history_frame, height=8)
        listbox.pack(fill=tk.BOTH, expand=True)

        # 阻止鼠标滚轮事件传播到主界面
        def on_listbox_mousewheel(event):
            listbox.yview_scroll(int(-1*(event.delta/120)), "units")
            return "break"  # 阻止事件传播

        listbox.bind("<MouseWheel>", on_listbox_mousewheel)  # Windows
        listbox.bind("<Button-4>", lambda e: listbox.yview_scroll(-1, "units") or "break")  # Linux
        listbox.bind("<Button-5>", lambda e: listbox.yview_scroll(1, "units") or "break")   # Linux

        # 填充历史记录
        if hasattr(self, 'move_history'):
            for path in self.move_history:
                listbox.insert(tk.END, path)

        def select_folder():
            folder = filedialog.askdirectory()
            if folder:
                move_to_folder(folder)

        def move_to_folder(target_folder):
            try:
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder)

                filename = os.path.basename(video_path)
                target_path = os.path.join(target_folder, filename)

                # 检查目标文件是否已存在
                if os.path.exists(target_path):
                    if not messagebox.askyesno("文件已存在",
                                           f"文件 {filename} 在目标文件夹中已存在，是否覆盖？"):
                        return

                # 停止视频预览
                self.video_player.stop_video()
                time.sleep(0.1)  # 等待资源释放

                # 移动文件
                shutil.move(video_path, target_path)

                # 清理已移动文件的缓存
                # 清理缩略图缓存
                if video_path in self.thumbnail_cache:
                    del self.thumbnail_cache[video_path]

                # 清理复选框状态
                if video_path in self.checkbox_vars:
                    del self.checkbox_vars[video_path]

                # 清理像素缓存
                if video_path in self.pixel_cache:
                    del self.pixel_cache[video_path]

                # 保存到历史记录
                self.save_move_history(target_folder)

                # 关闭对话框
                dialog.destroy()

                # 使用统一的完全重新加载方法
                self.complete_reload_after_delete()

                print(f"文件已移动到: {target_path}")  # 不显示成功提示，根据用户偏好

            except Exception as e:
                messagebox.showerror("错误", f"移动文件失败: {e}")

        # 双击历史记录项时移动到选中的文件夹
        listbox.bind('<Double-Button-1>',
                    lambda e: move_to_folder(listbox.get(listbox.curselection()))
                    if listbox.curselection() else None)

        # 添加按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="选择目标文件夹",
                 command=select_folder).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消",
                 command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def delete_current_file(self):
        """删除当前右键选中的文件"""
        if not hasattr(self, 'current_video'):
            return

        video_path = os.path.join(self.folder_path, self.current_video)
        if not os.path.exists(video_path):
            messagebox.showerror("错误", "文件不存在")
            return

        # 确认删除
        if not messagebox.askyesno("确认删除",
                                  f"确定要删除文件：\n{self.current_video}？",
                                  icon='warning'):
            return

        try:
            # 停止视频预览
            self.video_player.stop_video()
            time.sleep(0.1)  # 等待资源释放

            # 删除文件 - 使用支持特殊字符的删除方法
            delete_file_with_system(video_path)
            self.log_operation("删除", video_path)  # 记录删除操作

            # 更新显示 - 使用统一的完全重新加载方法
            self.complete_reload_after_delete()
            print(f"文件已删除: {self.current_video}")  # 不显示成功提示，根据用户偏好
        except Exception as e:
            messagebox.showerror("错误", f"删除失败: {str(e)}")

    def load_move_history(self):
        """加载移动历史记录"""
        history_file = "move_history.txt"
        try:
            if os.path.exists(history_file):
                with open(history_file, "r", encoding="utf-8") as f:
                    return [line.strip() for line in f if line.strip()]
            return []
        except Exception as e:
            print(f"加载历史记录出错: {e}")
            return []

    def save_move_history(self, path):
        """保存移动历史记录"""
        history_file = "move_history.txt"
        try:
            # 将新路径添加到历史记录开头
            history = self.move_history
            if path in history:
                history.remove(path)
            history.insert(0, path)
            # 只保留最近的20条记录（原来是10条）
            history = history[:20]
            self.move_history = history

            # 保存到文件
            with open(history_file, "w", encoding="utf-8") as f:
                for item in history:
                    f.write(f"{item}\n")
        except Exception as e:
            print(f"保存历史记录出错: {e}")

    def check_file_access(self, path):
        """检查文件是否被占用，优化后的版本"""
        try:
            if os.path.isfile(path):
                # 只检查顶层文件
                with open(path, 'ab') as f:
                    pass
            return True
        except (IOError, PermissionError):
            return False

    def force_release_video_handles(self, folder_path):
        """强制释放视频文件句柄"""
        try:
            print("正在强制释放视频文件句柄...")

            # 强制关闭所有OpenCV窗口
            cv2.destroyAllWindows()

            # 多次调用waitKey确保OpenCV完全释放资源
            for _ in range(10):
                cv2.waitKey(1)

            # 强制垃圾回收
            import gc
            gc.collect()

            # 尝试使用psutil查找并关闭占用文件的进程（如果可用）
            try:
                import psutil
                current_process = psutil.Process()

                # 检查当前进程打开的文件
                open_files = current_process.open_files()
                for file_info in open_files:
                    if folder_path in file_info.path:
                        print(f"发现占用的文件: {file_info.path}")

            except ImportError:
                print("psutil不可用，跳过进程检查")
            except Exception as e:
                print(f"检查进程文件句柄时出错: {e}")

            print("文件句柄释放完成")

        except Exception as e:
            print(f"强制释放文件句柄时出错: {e}")

    def async_generate_thumbnails(self, videos_to_display):
        """异步生成缩略图以提高响应速度 - 修复网格布局问题"""
        try:
            row, col = 0, 0
            self.thumbnail_labels = []

            # 首先过滤出存在的视频文件
            valid_videos = []
            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)
                if os.path.exists(video_path):
                    valid_videos.append(video)
                else:
                    print(f"跳过不存在的文件: {video_path}")

            # 创建所有框架和占位符
            video_frames = []
            for video in valid_videos:
                video_path = os.path.join(self.folder_path, video)

                # 创建框架
                frame = tk.Frame(self.content_frame)
                frame.grid(row=row, column=col, padx=10, pady=10)

                # 创建占位符标签
                placeholder_label = tk.Label(frame, text="加载中...", width=50, height=20, bg='lightgray')
                placeholder_label.pack()

                # 创建信息框架
                info_frame = tk.Frame(frame)
                info_frame.pack(fill=tk.X, padx=5)

                # 显示文件名
                name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                    wraplength=200)
                name_label.pack()

                # 创建复选框
                if video_path not in self.checkbox_vars:
                    self.checkbox_vars[video_path] = tk.BooleanVar()
                checkbox = tk.Checkbutton(frame, variable=self.checkbox_vars[video_path])
                checkbox.pack()

                video_frames.append({
                    'video': video,
                    'video_path': video_path,
                    'frame': frame,
                    'placeholder_label': placeholder_label,
                    'info_frame': info_frame,
                    'name_label': name_label,
                    'checkbox': checkbox
                })

                col += 1
                if col >= self.videos_per_row:
                    col = 0
                    row += 1

            # 更新画布滚动区
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

            # 异步生成缩略图
            def generate_thumbnails_async():
                for item in video_frames:
                    try:
                        # 检查是否还需要生成（用户可能已经切换页面）
                        if not item['placeholder_label'].winfo_exists():
                            continue

                        video_path = item['video_path']

                        # 生成缩略图
                        thumbnail = self.generate_thumbnail(video_path)

                        # 在主线程中更新UI
                        def update_ui():
                            try:
                                if item['placeholder_label'].winfo_exists():
                                    # 替换占位符
                                    item['placeholder_label'].destroy()

                                    if thumbnail is not None:
                                        # 成功生成缩略图
                                        thumbnail_image = ImageTk.PhotoImage(thumbnail)
                                        label = tk.Label(item['frame'], image=thumbnail_image, compound=tk.TOP)
                                        label.pack(before=item['info_frame'])

                                        # 绑定事件
                                        label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))
                                        label.bind("<Button-3>", lambda e, v=item['video']: self.show_context_menu(e, v))

                                        # 绑定预览事件
                                        def make_preview_handler(v_path, v_img, v_label):
                                            def handler(event):
                                                if v_label.winfo_exists():
                                                    self.show_preview(event, v_path, v_img)
                                            return handler

                                        preview_handler = make_preview_handler(video_path, thumbnail_image, label)
                                        label.bind("<Enter>", preview_handler)

                                        # 获取视频信息
                                        size_str, resolution, duration_str, _ = self.get_video_info(video_path)

                                        # 更新信息显示
                                        info_label = tk.Label(item['info_frame'],
                                                            text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
                                        info_label.pack()

                                        # 绑定右键菜单到名称标签
                                        item['name_label'].bind("<Button-3>", lambda e, v=item['video']: self.show_context_menu(e, v))

                                        self.thumbnail_labels.append((label, thumbnail_image, video_path))
                                    else:
                                        # 缩略图生成失败，显示错误信息
                                        error_label = tk.Label(item['frame'], text="无法加载\n预览图",
                                                             width=50, height=20, bg='lightcoral', fg='white')
                                        error_label.pack(before=item['info_frame'])

                                        # 仍然绑定右键菜单
                                        error_label.bind("<Button-3>", lambda e, v=item['video']: self.show_context_menu(e, v))

                                        # 显示基本信息
                                        try:
                                            file_size = os.path.getsize(video_path)
                                            size_str = f"{file_size/(1024*1024):.1f}MB" if file_size >= 1024*1024 else f"{file_size/1024:.1f}KB"
                                            info_label = tk.Label(item['info_frame'], text=f"大小: {size_str}\n状态: 无法预览")
                                            info_label.pack()
                                        except:
                                            info_label = tk.Label(item['info_frame'], text="状态: 无法预览")
                                            info_label.pack()

                            except Exception as e:
                                print(f"更新UI时出错: {e}")

                        # 在主线程中执行UI更新
                        self.master.after(0, update_ui)

                    except Exception as e:
                        print(f"异步生成缩略图时出错: {e}")

            # 在后台线程中生成缩略图
            threading.Thread(target=generate_thumbnails_async, daemon=True).start()

        except Exception as e:
            print(f"异步生成缩略图失败: {e}")

    def release_all_resources(self):
        """释放所有资源"""
        try:
            # 停止所有视频预览
            if hasattr(self, 'video_player'):
                self.video_player.stop_video()

            # 关闭线程池并等待所有任务完成
            if hasattr(self, '_thumbnail_executor'):
                self._thumbnail_executor.shutdown(wait=True)
                delattr(self, '_thumbnail_executor')

            # 清理所有标签引用和图像
            for widget in self.content_frame.winfo_children():
                if isinstance(widget, tk.Label):
                    widget.image = None
                widget.destroy()

            # 清理缩略图缓存
            if hasattr(self, 'thumbnail_cache'):
                self.thumbnail_cache.clear()

            # 清理像素缓存
            if hasattr(self, 'pixel_cache'):
                self.pixel_cache.clear()

            # 清理视频信息缓存
            if hasattr(self, 'video_info_cache'):
                self.video_info_cache.clear()

            # 清理缩略图标签列表
            if hasattr(self, 'thumbnail_labels'):
                for label, image, _ in self.thumbnail_labels:
                    label.image = None
                self.thumbnail_labels.clear()

            # 清理复选框变量
            if hasattr(self, 'checkbox_vars'):
                self.checkbox_vars.clear()

            # 强制垃圾回收
            import gc
            gc.collect()

            # 增加等待时间确保所有文件句柄都被释放
            print("等待文件句柄释放...")
            time.sleep(1.0)  # 增加等待时间到1秒

            # 再次强制垃圾回收
            gc.collect()
            time.sleep(0.5)  # 再等待0.5秒

            print("资源释放完成")

        except Exception as e:
            print(f"释放资源时出错: {e}")

    def move_to_folder(self, target_folder):
        """移动到目标文件夹"""
        try:
            if not os.path.exists(target_folder):
                os.makedirs(target_folder)

            # 获取源文件夹名称和原父文件夹路径
            source_folder_name = os.path.basename(self.folder_path)
            original_parent = os.path.dirname(self.folder_path)
            new_folder_path = os.path.join(target_folder, source_folder_name)

            # 检查目标文件夹是否已存在
            if os.path.exists(new_folder_path):
                if not messagebox.askyesno("文件夹已存在",
                                         f"文件夹 {source_folder_name} 在目标位置已存在，是否覆盖？"):
                    return
                shutil.rmtree(new_folder_path)

            # 关闭主界面
            self.master.withdraw()  # 隐藏主窗口
            self.release_all_resources()  # 释放所有资源

            # 强制释放视频文件句柄
            self.force_release_video_handles(self.folder_path)

            # 多次检查文件访问权限，确保资源完全释放
            max_retries = 3
            for attempt in range(max_retries):
                if self.check_file_access(self.folder_path):
                    print(f"文件访问检查通过 (尝试 {attempt + 1}/{max_retries})")
                    break
                else:
                    print(f"文件被占用，等待资源释放... (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        # 在重试前再次强制释放资源
                        self.force_release_video_handles(self.folder_path)
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        self.master.deiconify()  # 如果失败，重新显示主窗口
                        messagebox.showerror("错误", "文件被占用，无法移动。请关闭所有正在使用的文件后重试。\n\n可能原因：预览图生成进程仍在运行，请稍后再试。")
                        return

            # 如果没有文件被占用，执行实际移动
            try:
                # 诊断文件夹权限问题
                self.diagnose_folder_permissions(self.folder_path, target_folder)

                # 使用系统方法移动文件夹
                move_file_with_system(self.folder_path, new_folder_path)

                # 记录移动操作
                self.log_operation("移动", self.folder_path, new_folder_path)

                # 保存到历史记录
                self.save_move_history(target_folder)

                # 将原父文件夹设为默认文件夹
                with open(self.default_folder_file, "w", encoding="utf-8") as f:
                    f.write(original_parent)

                # 清空当前文件夹路径
                self.folder_path = ""

                # 清理所有缓存和显示
                self.clear_all_caches()
                self.clear_canvas()
                self.page_label.config(text="0/0")

                # 重新显示主窗口并打开文件夹选择对话框
                self.master.deiconify()
                print(f"文件夹已移动到: {new_folder_path}")  # 不显示成功提示，根据用户偏好
                self.open_folder()

            except Exception as e:
                self.master.deiconify()  # 如果失败，重新显示主窗口
                messagebox.showerror("错误", f"移动文件夹失败: {e}")

        except Exception as e:
            self.master.deiconify()  # 如果失败，重新显示主窗口
            messagebox.showerror("错误", f"移动文件夹失败: {e}")

    def diagnose_folder_permissions(self, source_folder, target_folder):
        """静默检查文件夹权限问题"""
        # 简化的权限检查，不输出任何调试信息
        pass

    def show_move_folder_dialog(self):
        """显示移动文件夹对话框"""
        if not self.folder_path:
            messagebox.showerror("错误", "请先打开文件夹")
            return

        dialog = tk.Toplevel(self.master)
        dialog.title("移动到文件夹")
        dialog.geometry("400x300")
        dialog.transient(self.master)

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.master.winfo_rootx() + self.master.winfo_width()/2 - 200,
            self.master.winfo_rooty() + self.master.winfo_height()/2 - 150
        ))

        # 创建历史记录列表框
        history_frame = tk.Frame(dialog)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        tk.Label(history_frame, text="最近使用的目标文件夹:").pack(anchor=tk.W)

        listbox = tk.Listbox(history_frame, height=8)
        listbox.pack(fill=tk.BOTH, expand=True)

        # 阻止鼠标滚轮事件传播到主界面
        def on_listbox_mousewheel(event):
            listbox.yview_scroll(int(-1*(event.delta/120)), "units")
            return "break"  # 阻止事件传播

        listbox.bind("<MouseWheel>", on_listbox_mousewheel)  # Windows
        listbox.bind("<Button-4>", lambda e: listbox.yview_scroll(-1, "units") or "break")  # Linux
        listbox.bind("<Button-5>", lambda e: listbox.yview_scroll(1, "units") or "break")   # Linux

        # 填充历史记录
        if hasattr(self, 'move_history'):
            for path in self.move_history:
                listbox.insert(tk.END, path)

        def select_folder():
            folder = filedialog.askdirectory()
            if folder:
                move_to_folder(folder)

        def move_to_folder(target_folder):
            # 关闭对话框
            dialog.destroy()
            # 执行移动操作
            self.move_to_folder(target_folder)

        # 双击历史记录项时移动到选中的文件夹
        listbox.bind('<Double-Button-1>',
                    lambda e: move_to_folder(listbox.get(listbox.curselection()))
                    if listbox.curselection() else None)

        # 添加按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="选择目标文件夹",
                 command=select_folder).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消",
                 command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def confirm_delete_folder(self):
        """确认并删除当前文件夹"""
        if not self.folder_path:
            messagebox.showerror("错误", "请先打开文件夹")
            return

        # 获取文件夹名称和父文件夹路径
        folder_name = os.path.basename(self.folder_path)
        parent_folder = os.path.dirname(self.folder_path)

        # 确认删除
        if not messagebox.askyesno("删除确认", f"确定要删除文件夹：{folder_name}？"):
            return

        try:
            # 停止所有视频预览
            if hasattr(self, 'video_player'):
                self.video_player.stop_video()

            # 等待资源释放
            time.sleep(0.1)

            # 记录删除操作
            self.log_operation("删除", self.folder_path)

            # 记录到删除索引 - 只记录路径
            with open(self.index_path, "a", encoding="utf-8") as f:
                f.write(f"{self.folder_path}\n")

            # 删除文件夹及其内容 - 使用支持特殊字符的删除方法
            try:
                delete_folder_with_system(self.folder_path)
                # 验证文件夹是否真的被删除
                if not os.path.exists(self.folder_path):
                    # 将父文件夹设为默认文件夹
                    with open(self.default_folder_file, "w", encoding="utf-8") as f:
                        f.write(parent_folder)

                    # 清空当前文件夹路径
                    self.folder_path = ""

                    # 清理所有缓存和界面
                    self.clear_all_caches()
                    self.clear_canvas()
                    self.page_label.config(text="0/0")

                    # 显示成功消息
                    messagebox.showinfo("成功", f"文件夹 {folder_name} 已删除")

                    # 自动弹出文件夹选择对话框
                    self.open_folder()
                else:
                    raise Exception("文件夹删除失败，文件夹仍然存在")
            except Exception as e:
                raise Exception(f"删除文件夹失败: {str(e)}")

        except Exception as e:
            messagebox.showerror("错误", f"删除文件夹失败: {e}")

    def log_operation(self, operation_type, file_path, target_path=None):
        """记录操作到日志文件
        Args:
            operation_type: 操作类型（删除/移动）
            file_path: 操作的文件路径
            target_path: 移动操作的目标路径（可选）
        """
        try:
            # 确保日志目录存在
            if not os.path.exists(self.log_dir):
                os.makedirs(self.log_dir)

            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            if operation_type == "删除":
                log_entry = f"[{current_time}] {operation_type}: {file_path}\n"
            else:  # 移动操作
                log_entry = f"[{current_time}] {operation_type}: {file_path} -> {target_path}\n"

            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_entry)
        except Exception as e:
            print(f"写入日志失败: {e}")

    def load_last_folder(self):
        """加载上次打开的文件夹路径"""
        try:
            if os.path.exists(self.last_folder_file):
                with open(self.last_folder_file, "r", encoding="utf-8") as f:
                    path = f.read().strip()
                    return path if os.path.exists(path) else ""
        except Exception as e:
            print(f"读取上次文件夹失败: {e}")
        return ""

    def save_last_folder(self, folder_path):
        """保存当前打开的文件夹路径"""
        try:
            with open(self.last_folder_file, "w", encoding="utf-8") as f:
                f.write(folder_path)
        except Exception as e:
            print(f"保存文件夹路径失败: {e}")

    def load_default_folder(self):
        """加载默认文件夹路径"""
        try:
            if os.path.exists(self.default_folder_file):
                with open(self.default_folder_file, "r", encoding="utf-8") as f:
                    path = f.read().strip()
                    return path if os.path.exists(path) else ""
        except Exception as e:
            print(f"读取默认文件夹失败: {e}")
        return ""

    def save_default_folder(self, folder_path):
        """保存默认文件夹路径（仅在第一次设置时保存父文件夹）"""
        try:
            if not os.path.exists(self.default_folder_file):
                # 获取父文件夹路径
                parent_folder = os.path.dirname(folder_path)
                with open(self.default_folder_file, "w", encoding="utf-8") as f:
                    f.write(parent_folder)
        except Exception as e:
            print(f"保存默认文件夹失败: {e}")

    def open_images(self):
        """使用图片预览查看管理器.py打开当前文件夹的图片"""
        if not self.folder_path:
            messagebox.showerror("错误", "请先打开文件夹")
            return

        # 检查图片预览查看管理器.py是否存在
        if not os.path.exists(self.image_viewer):
            messagebox.showerror("错误", "未找到图片预览查看管理器.py")
            return

        try:
            # 获取当前Python解释器路径
            python_executable = sys.executable

            # 使用短路径来避免中文路径问题
            import win32api
            short_path_executable = win32api.GetShortPathName(python_executable)
            short_path_script = win32api.GetShortPathName(self.image_viewer)
            short_path_folder = win32api.GetShortPathName(self.folder_path)

            # 使用短路径启动图片预览查看管理器.py
            process = subprocess.Popen([short_path_executable, short_path_script, short_path_folder],
                                     stderr=subprocess.PIPE,
                                     stdout=subprocess.PIPE,
                                     universal_newlines=True)

            # 检查是否有错误输出
            stderr = process.stderr.read()
            if stderr:
                if "ModuleNotFoundError: No module named 'PIL'" in stderr:
                    messagebox.showerror("错误",
                        "缺少必要的图片处理库。\n请使用以下命令安装:\npip install Pillow")
                else:
                    messagebox.showerror("错误", f"启动失败: {stderr}")
                return

        except ImportError:
            # 如果win32api不可用，提示安装
            messagebox.showerror("错误",
                "缺少必要的库。\n请使用以下命令安装:\npip install pywin32")
        except Exception as e:
            messagebox.showerror("错误", f"打开图片失败: {e}")

    def show_video_preview(self, force_rescan=False):
        """显示视频预览 - 优化版本，支持选择性重新扫描"""
        if not self.folder_path:
            return

        # 更新文件夹名显示
        self.folder_label.config(text=f"当前文件夹: {os.path.basename(self.folder_path)}")

        # 只在必要时重新扫描文件系统
        if force_rescan or not hasattr(self, 'video_files') or not self.video_files:
            self.video_files = self.get_video_files()

        if not self.video_files:  # 如果没有视频文件，直接返回
            self.page_label.config(text="0/0")
            return

        total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page

        # 更新页码显示
        self.page_label.config(text=f"{self.current_page}/{total_pages}")

        start_index = (self.current_page - 1) * self.videos_per_page
        end_index = start_index + self.videos_per_page

        videos_to_display = self.video_files[start_index:end_index]

        # 快速验证：只检查当前页的文件是否存在
        valid_videos = []
        invalid_videos = []
        for video in videos_to_display:
            video_path = os.path.join(self.folder_path, video)
            if os.path.exists(video_path):
                valid_videos.append(video)
            else:
                invalid_videos.append(video)
                print(f"发现不存在的文件: {video_path}")

        # 如果发现不存在的文件，从内存列表中移除
        if invalid_videos:
            for invalid_video in invalid_videos:
                if invalid_video in self.video_files:
                    self.video_files.remove(invalid_video)
            print(f"从内存列表中移除了 {len(invalid_videos)} 个不存在的文件")

        videos_to_display = valid_videos

        # 清理现有内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 清理不存在文件的复选框状态（只清理当前页相关的）
        current_page_paths = set(os.path.join(self.folder_path, video) for video in videos_to_display)
        invalid_checkbox_paths = []
        for path in self.checkbox_vars.keys():
            # 检查是否是当前页的文件且不存在
            if any(path.endswith(video) for video in invalid_videos):
                invalid_checkbox_paths.append(path)

        for path in invalid_checkbox_paths:
            del self.checkbox_vars[path]

        # 使用完全同步的缩略图生成方法，禁用所有异步操作
        self.fully_sync_generate_thumbnails(videos_to_display)

        # 更新画布滚动区
        self.content_frame.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        # 启动后台预加载相邻页面的缩略图
        if hasattr(self, 'preload_adjacent_thumbnails'):
            self.preload_adjacent_thumbnails()

    def fully_sync_generate_thumbnails(self, videos_to_display):
        """完全同步的缩略图生成方法 - 只显示存在的文件，删除的文件不占位置"""
        try:
            # 清理现有内容
            for widget in self.content_frame.winfo_children():
                widget.destroy()

            row, col = 0, 0
            self.thumbnail_labels = []
            successfully_displayed = 0

            for i, video in enumerate(videos_to_display):
                video_path = os.path.join(self.folder_path, video)

                # 检查文件是否存在 - 不存在的文件直接跳过，不占用网格位置
                if not os.path.exists(video_path):
                    continue  # 直接跳过，不创建框架，不占用网格位置

                # 只为存在的文件创建框架
                frame = tk.Frame(self.content_frame)
                frame.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

                # 配置框架的网格权重
                frame.grid_rowconfigure(0, weight=1)  # 缩略图区域
                frame.grid_rowconfigure(1, weight=0)  # 信息区域
                frame.grid_columnconfigure(0, weight=1)

                # 同步生成缩略图
                thumbnail_success = False

                try:
                    # 检查缓存
                    thumbnail = None
                    if video_path in self.thumbnail_cache:
                        try:
                            thumbnail = self.thumbnail_cache[video_path]
                        except Exception as e:
                            del self.thumbnail_cache[video_path]

                    # 生成新缩略图
                    if thumbnail is None:
                        thumbnail = self.generate_thumbnail(video_path)

                    # 创建缩略图显示
                    if thumbnail is not None:
                        thumbnail_image = ImageTk.PhotoImage(thumbnail)
                        label = tk.Label(frame, image=thumbnail_image)
                        label.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

                        # 绑定事件
                        label.bind("<Double-Button-1>", lambda e, path=video_path: self.open_fullscreen_player(path))
                        label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                        # 绑定预览事件
                        def make_preview_handler(v_path, v_img, v_label):
                            def handler(event):
                                if v_label.winfo_exists():
                                    self.show_preview(event, v_path, v_img)
                            return handler

                        preview_handler = make_preview_handler(video_path, thumbnail_image, label)
                        label.bind("<Enter>", preview_handler)

                        self.thumbnail_labels.append((label, thumbnail_image, video_path))
                        thumbnail_success = True

                except Exception as e:
                    pass

                # 如果缩略图生成失败，显示错误信息
                if not thumbnail_success:
                    error_label = tk.Label(frame, text="无法生成\n预览图",
                                         width=25, height=15, bg='lightcoral', fg='white')
                    error_label.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
                    error_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 创建信息区域 - 使用grid布局
                info_frame = tk.Frame(frame)
                info_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)

                # 配置信息框架的网格
                info_frame.grid_columnconfigure(0, weight=1)

                # 文件名 - 使用原来的样式，不指定字体（使用系统默认）
                name_label = tk.Label(info_frame, text=video[:40] + "..." if len(video) > 40 else video,
                                    wraplength=200)
                name_label.grid(row=0, column=0, sticky="ew")
                name_label.bind("<Button-3>", lambda e, v=video: self.show_context_menu(e, v))

                # 文件信息 - 使用原来的样式，不指定字体
                try:
                    size_str, resolution, duration_str, _ = self.get_video_info(video_path)
                    info_label = tk.Label(info_frame,
                                        text=f"大小: {size_str}\n分辨率: {resolution}\n时长: {duration_str}")
                    info_label.grid(row=1, column=0, sticky="ew")
                except Exception as e:
                    print(f"获取视频信息失败: {e}")
                    info_label = tk.Label(info_frame, text="信息获取失败")
                    info_label.grid(row=1, column=0, sticky="ew")

                # 复选框
                if video_path not in self.checkbox_vars:
                    self.checkbox_vars[video_path] = tk.BooleanVar()
                checkbox = tk.Checkbutton(info_frame, variable=self.checkbox_vars[video_path])
                checkbox.grid(row=2, column=0)

                # 只有成功创建了框架才更新网格位置和计数
                successfully_displayed += 1
                col += 1
                if col >= self.videos_per_row:
                    col = 0
                    row += 1

                # 强制更新UI，确保用户能看到进度
                self.master.update_idletasks()

            # 最终更新画布
            self.content_frame.update_idletasks()
            self.canvas.configure(scrollregion=self.canvas.bbox("all"))

        except Exception as e:
            # 显示错误信息
            error_label = tk.Label(self.content_frame, text=f"生成失败: {e}",
                                 font=("Arial", 12), fg='red')
            error_label.grid(row=0, column=0, pady=20)

    def keep_selected_videos(self):
        """保留选中的视频，删除所有页面中未选中的视频"""
        # 获取所有未选中的视频
        unselected_videos = []
        for video in self.video_files:  # 遍历所有视频文件
            video_path = os.path.join(self.folder_path, video)
            if video_path in self.checkbox_vars and not self.checkbox_vars[video_path].get():
                unselected_videos.append(video_path)

        if not unselected_videos:
            messagebox.showinfo("提示", "没有未选中的视频")
            return

        # 确认删除
        result = messagebox.askyesno("删除确认", 
                                   f"确定要删除所有页面中的 {len(unselected_videos)} 个未选中的视频吗？")
        if not result:
            return

        try:
            # 停止所有视频预览
            self.video_player.stop_video()

            # 清理所有标签引用
            for label, _, _ in self.thumbnail_labels:
                label.image = None
                label.destroy()
            self.thumbnail_labels.clear()

            # 清理画布
            self.clear_canvas()

            # 强制垃圾回收
            import gc
            gc.collect()

            # 减少等待时间
            time.sleep(0.2)

            # 删除未选中的视频
            deleted_files = []
            for video_path in unselected_videos:
                try:
                    if os.path.exists(video_path):
                        delete_file_with_system(video_path)
                        self.log_operation("删除", video_path)  # 记录删除操作
                        deleted_files.append(video_path)
                except Exception as e:
                    print(f"删除文件失败: {video_path}, 错误: {e}")

            # 从video_files列表中移除已删除的文件
            for file in deleted_files:
                rel_path = os.path.relpath(file, self.folder_path)
                if rel_path in self.video_files:
                    self.video_files.remove(rel_path)

            # 更新界面
            self.show_video_preview()
            messagebox.showinfo("完成", "删除操作已完成")

        except Exception as e:
            print(f"批量删除出错: {e}")
            messagebox.showerror("错误", f"删除过程中出错: {e}")

    def show_move_selected_dialog(self):
        """显示移动选中视频的对话框"""
        if not self.folder_path:
            messagebox.showerror("错误", "请先打开文件夹")
            return

        # 获取选中的视频
        selected_videos = [path for path, var in self.checkbox_vars.items() if var.get()]

        if not selected_videos:
            messagebox.showerror("错误", "请先选择要移动的视频")
            return

        dialog = tk.Toplevel(self.master)
        dialog.title("移动选中的视频")
        dialog.geometry("400x300")
        dialog.transient(self.master)

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.master.winfo_rootx() + self.master.winfo_width()/2 - 200,
            self.master.winfo_rooty() + self.master.winfo_height()/2 - 150
        ))

        # 创建历史记录列表框
        history_frame = tk.Frame(dialog)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        tk.Label(history_frame, text="最近使用的目标文件夹:").pack(anchor=tk.W)

        listbox = tk.Listbox(history_frame, height=8)
        listbox.pack(fill=tk.BOTH, expand=True)

        # 阻止鼠标滚轮事件传播到主界面
        def on_listbox_mousewheel(event):
            listbox.yview_scroll(int(-1*(event.delta/120)), "units")
            return "break"  # 阻止事件传播

        listbox.bind("<MouseWheel>", on_listbox_mousewheel)  # Windows
        listbox.bind("<Button-4>", lambda e: listbox.yview_scroll(-1, "units") or "break")  # Linux
        listbox.bind("<Button-5>", lambda e: listbox.yview_scroll(1, "units") or "break")   # Linux

        # 填充历史记录
        if hasattr(self, 'move_history'):
            for path in self.move_history:
                listbox.insert(tk.END, path)

        def select_folder():
            folder = filedialog.askdirectory()
            if folder:
                move_to_folder(folder, selected_videos)

        def move_to_folder(target_folder, selected_videos):
            try:
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder)

                # 停止视频预览
                self.video_player.stop_video()
                time.sleep(0.1)  # 等待资源释放

                moved_files = []
                failed_files = []

                # 批量移动所有选中的视频
                for video_path in selected_videos:
                    try:
                        filename = os.path.basename(video_path)
                        target_path = os.path.join(target_folder, filename)

                        # 检查目标文件是否已存在
                        if os.path.exists(target_path):
                            if not messagebox.askyesno("文件已存在",
                                                   f"文件 {filename} 在目标文件夹中已存在，是否覆盖？"):
                                continue

                        # 移动文件
                        shutil.move(video_path, target_path)
                        moved_files.append(video_path)

                    except Exception as e:
                        print(f"移动文件失败: {video_path}, 错误: {e}")
                        failed_files.append(video_path)

                # 批量清理已移动文件的缓存
                if moved_files:
                    self.batch_cleanup_moved_files(moved_files)

                # 保存到历史记录
                self.save_move_history(target_folder)

                # 关闭对话框
                dialog.destroy()

                # 使用统一的完全重新加载方法
                if moved_files:
                    self.complete_reload_after_delete()

                # 显示结果
                if failed_files:
                    messagebox.showwarning("部分完成",
                        f"成功移动 {len(moved_files)} 个文件，{len(failed_files)} 个文件移动失败")
                else:
                    print(f"已将 {len(moved_files)} 个视频移动到: {target_folder}")  # 不显示成功提示，根据用户偏好

            except Exception as e:
                messagebox.showerror("错误", f"移动文件失败: {e}")

        # 双击历史记录项时移动到选中的文件夹
        listbox.bind('<Double-Button-1>',
                    lambda e: move_to_folder(listbox.get(listbox.curselection()), selected_videos)
                    if listbox.curselection() else None)

        # 添加按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="选择目标文件夹",
                 command=select_folder).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消",
                 command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def get_current_page_videos(self):
        """获取当前页面显示的视频列表"""
        start_index = (self.current_page - 1) * self.videos_per_page
        end_index = start_index + self.videos_per_page
        return self.video_files[start_index:end_index]

    def refresh_current_page(self):
        """只刷新当前页面"""
        # 保存当前滚动位置
        scroll_position = self.canvas.yview()[0]
        
        # 清理当前页面的标签
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # 重新生成当前页面的预览
        self.show_video_preview()
        
        # 恢复滚动位置
        self.canvas.yview_moveto(scroll_position)

    def update_page_info(self):
        """只更新页码信息"""
        total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page
        self.page_label.config(text=f"{self.current_page}/{total_pages}")

    def manage_thumbnail_cache(self):
        """智能管理缩略图缓存"""
        try:
            # 设置缓存大小限制（例如200MB）
            cache_size_limit = 200 * 1024 * 1024
            current_size = 0

            # 计算当前缓存大小
            for thumbnail in self.thumbnail_cache.values():
                try:
                    current_size += len(thumbnail.tobytes())
                except:
                    # 如果无法计算大小，估算为1MB
                    current_size += 1024 * 1024



            # 如果超过限制，智能清理缓存
            if current_size > cache_size_limit:

                # 获取当前页面的视频路径，这些不应该被清理
                current_page_paths = set()
                if hasattr(self, 'video_files') and self.video_files:
                    start_index = (self.current_page - 1) * self.videos_per_page
                    end_index = start_index + self.videos_per_page
                    current_videos = self.video_files[start_index:end_index]
                    current_page_paths = set(os.path.join(self.folder_path, video) for video in current_videos)

                # 清理不在当前页面的缓存
                paths_to_remove = []
                for path in self.thumbnail_cache.keys():
                    if path not in current_page_paths:
                        paths_to_remove.append(path)

                # 清理一半的非当前页缓存
                remove_count = min(len(paths_to_remove), len(self.thumbnail_cache) // 2)
                for i, path in enumerate(paths_to_remove):
                    if i >= remove_count:
                        break
                    del self.thumbnail_cache[path]



        except Exception as e:
            print(f"管理缩略图缓存时出错: {e}")

    def preload_adjacent_thumbnails(self):
        """预加载相邻页面的缩略图"""
        try:
            if not hasattr(self, 'video_files') or not self.video_files:
                return

            total_pages = (len(self.video_files) + self.videos_per_page - 1) // self.videos_per_page

            # 预加载前一页和后一页的缩略图
            pages_to_preload = []
            if self.current_page > 1:
                pages_to_preload.append(self.current_page - 1)
            if self.current_page < total_pages:
                pages_to_preload.append(self.current_page + 1)

            def preload_worker():
                for page in pages_to_preload:
                    start_index = (page - 1) * self.videos_per_page
                    end_index = start_index + self.videos_per_page
                    videos_to_preload = self.video_files[start_index:end_index]

                    for video in videos_to_preload:
                        video_path = os.path.join(self.folder_path, video)
                        if video_path not in self.thumbnail_cache and os.path.exists(video_path):
                            try:
                                thumbnail = self.generate_thumbnail(video_path)
                                if thumbnail:
                                    # 缓存已在generate_thumbnail中处理
                                    pass
                            except Exception as e:

                                break  # 如果出错就停止预加载

            # 在后台线程中预加载
            threading.Thread(target=preload_worker, daemon=True).start()

        except Exception as e:
            print(f"预加载相邻缩略图时出错: {e}")

class FullscreenPlayer:
    def __init__(self, video_path, video_list=None, on_delete=None, move_history=None, main_app=None):
        self.video_path = video_path
        self.video_list = video_list
        self.on_delete = on_delete
        self.move_history = move_history
        self.main_app = main_app  # 存储主应用实例引用
        self.calculate_scaled_dimensions = calculate_scaled_dimensions
        
        # 添加标记状态 - 移到前面来初始化
        self.marked = tk.BooleanVar()
        
        # 添加向前按钮点击时间记录
        self.last_forward_click = 0
        self.last_forward_position = 0
        # 添加是否已经预留过40秒的标志
        self.has_reserved_buffer = False

        # 添加当前视频索引
        if video_list:
            try:
                self.current_index = video_list.index(video_path)
            except ValueError:
                self.current_index = 0
        else:
            self.current_index = 0

        # 创建播放窗口
        self.window = tk.Toplevel()
        self.window.title("视频播放")
        self.window.geometry("1080x768")

        # 创建视频显示框架
        self.video_frame = tk.Frame(self.window, bg='black')
        self.video_frame.pack(expand=True, fill=tk.BOTH)

        # 创建视频显示标签
        self.video_label = tk.Label(self.video_frame, bg='black')
        self.video_label.pack(expand=True, fill=tk.BOTH)

        # 初始化VLC实例和播放器
        self.instance = vlc.Instance()
        self.player = self.instance.media_player_new()

        # 设置播放窗口
        self.player.set_hwnd(self.video_label.winfo_id())

        # 加载并播放视频
        self.load_video(video_path)

        # 绑定窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.close)
        self.window.bind('<Escape>', lambda e: self.close())

        # 绑定鼠标事件
        self.video_frame.bind("<Button-1>", self.on_left_click)  # 左键后退10秒
        self.video_frame.bind("<Button-3>", self.on_right_click)  # 右键前进10秒

        # 创建进度条
        self.create_progress_bar()

        # 创建控制按钮
        self.create_control_buttons()

        # 开始更新时间显示
        self.update_time_display()

        # 绑定 Del 键事件
        self.window.bind('<Delete>', lambda e: self.delete_video(show_confirm=True))
        
        # 绑定方向键和Page Up/Down键
        self.window.bind('<Left>', lambda e: self.jump_backward())
        self.window.bind('<Right>', lambda e: self.jump_forward())
        self.window.bind('<Prior>', lambda e: self.prev_video())  # Page Up
        self.window.bind('<Next>', lambda e: self.next_video())   # Page Down
        
        # 绑定上下键为按秒跳转
        self.window.bind('<Up>', lambda e: self.jump_second_backward())     # 上键后退1秒
        self.window.bind('<Down>', lambda e: self.jump_second_forward())   # 下键前进1秒
        
        # 在所有初始化完成后，检查当前视频在主界面是否被标记
        self.check_if_marked()

    def create_progress_bar(self):
        """创建进度条"""
        progress_frame = tk.Frame(self.window)
        progress_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = tk.Scale(progress_frame,
                                   variable=self.progress_var,
                                   from_=0, to=100,
                                   orient=tk.HORIZONTAL,
                                   showvalue=False,
                                   command=self.seek_video)
        self.progress_bar.pack(fill=tk.X, expand=True)

    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.Frame(self.window)
        control_frame.pack(side=tk.BOTTOM, pady=10)

        # 添加上一个按钮
        prev_button = tk.Button(control_frame, text="上一个", command=self.prev_video)
        prev_button.pack(side=tk.LEFT, padx=5)

        # 播放/暂停按钮
        self.is_playing = True
        self.play_button = tk.Button(control_frame, text="暂停", command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=5)

        # 添加下一个按钮
        next_button = tk.Button(control_frame, text="下一个", command=self.next_video)
        next_button.pack(side=tk.LEFT, padx=5)

        # 添加向前1/6按钮
        forward_button = tk.Button(control_frame, text="向前1/6", command=self.forward_one_sixth)
        forward_button.pack(side=tk.LEFT, padx=5)
        
        # 添加标记复选框
        self.mark_checkbox = tk.Checkbutton(control_frame, text="标记", 
                                          variable=self.marked, 
                                          command=self.toggle_mark)
        self.mark_checkbox.pack(side=tk.LEFT, padx=5)

        # 添加移动到按钮
        move_button = tk.Button(control_frame, text="移动到", command=self.show_move_dialog)
        move_button.pack(side=tk.LEFT, padx=5)

        # 添加删除按钮
        delete_button = tk.Button(control_frame, text="删除", command=self.delete_video)
        delete_button.pack(side=tk.LEFT, padx=5)

        # 添加关闭按钮
        close_button = tk.Button(control_frame, text="关闭", command=self.close)
        close_button.pack(side=tk.LEFT, padx=5)

        # 时间显示标签
        self.time_label = tk.Label(control_frame, text="00:00:00 / 00:00:00", width=20)
        self.time_label.pack(side=tk.LEFT, padx=5)

    def prev_video(self):
        """播放上一个视频"""
        if self.video_list and self.current_index > 0:
            self.current_index -= 1
            self.video_path = self.video_list[self.current_index]
            self.load_video(self.video_path)
            # 切换视频后更新标记状态
            self.check_if_marked()

    def next_video(self):
        """播放下一个视频"""
        if self.video_list and self.current_index < len(self.video_list) - 1:
            self.current_index += 1
            self.video_path = self.video_list[self.current_index]
            self.load_video(self.video_path)
            # 切换视频后更新标记状态
            self.check_if_marked()

    def forward_one_sixth(self):
        """向前跳转视频总时长的1/6，在最后40秒区域内使用10秒步进"""
        duration = self.player.get_length()
        if duration <= 0:
            return
            
        current_time = self.player.get_time()
        current_click = time.time()
        
        # 预留40秒（40000毫秒）
        buffer_time = 40000
        # 连续点击的时间阈值（1秒内的点击视为连续点击）
        click_threshold = 1.0
        
        # 检查是否在最后40秒区域内
        is_in_buffer_zone = (duration - current_time) <= buffer_time
        # 检查是否是连续点击
        is_continuous_click = (current_click - self.last_forward_click) < click_threshold
        
        if is_in_buffer_zone:
            # 在最后40秒区域内，使用10秒步进
            new_time = min(duration, current_time + 10000)
        else:
            # 不在最后40秒区域内，跳转1/6
            jump_time = duration // 6
            new_time = min(duration - buffer_time, current_time + jump_time)
        
        # 更新最后点击时间和位置
        self.last_forward_click = current_click
        self.last_forward_position = current_time
        
        # 执行跳转
        self.player.set_time(new_time)

    def show_move_dialog(self):
        """显示移动到对话框"""
        if not self.video_path:
            return

        dialog = tk.Toplevel(self.window)
        dialog.title("移动到文件夹")
        dialog.geometry("400x300")
        dialog.transient(self.window)

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.window.winfo_rootx() + self.window.winfo_width()/2 - 200,
            self.window.winfo_rooty() + self.window.winfo_height()/2 - 150
        ))

        # 创建历史记录列表框
        history_frame = tk.Frame(dialog)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        tk.Label(history_frame, text="最近使用的目标文件夹:").pack(anchor=tk.W)

        listbox = tk.Listbox(history_frame, height=8)
        listbox.pack(fill=tk.BOTH, expand=True)

        # 阻止鼠标滚轮事件传播到主界面
        def on_listbox_mousewheel(event):
            listbox.yview_scroll(int(-1*(event.delta/120)), "units")
            return "break"  # 阻止事件传播

        listbox.bind("<MouseWheel>", on_listbox_mousewheel)  # Windows
        listbox.bind("<Button-4>", lambda e: listbox.yview_scroll(-1, "units") or "break")  # Linux
        listbox.bind("<Button-5>", lambda e: listbox.yview_scroll(1, "units") or "break")   # Linux

        # 填充历史记录
        if self.move_history:  # 使用传入的历史记录
            for path in self.move_history:
                listbox.insert(tk.END, path)

        def select_folder():
            folder = filedialog.askdirectory()
            if folder:
                move_to_folder(folder)

        # 双击历史记录项时移动到选中的文件夹
        listbox.bind('<Double-Button-1>', 
                    lambda e: move_to_folder(listbox.get(listbox.curselection())) 
                    if listbox.curselection() else None)

        # 添加按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=10)

        # 添加选择文件夹和取消按钮
        tk.Button(button_frame, text="选择目标文件夹", 
                 command=select_folder).pack(side=tk.LEFT, padx=10)
        tk.Button(button_frame, text="取消", 
                 command=dialog.destroy).pack(side=tk.LEFT, padx=10)

        def move_to_folder(target_folder):
            try:
                if not os.path.exists(target_folder):
                    os.makedirs(target_folder)

                # 获取源文件名
                filename = os.path.basename(self.video_path)
                target_path = os.path.join(target_folder, filename)

                # 检查目标文件是否已存在
                if os.path.exists(target_path):
                    if not messagebox.askyesno("文件已存在",
                                             f"文件 {filename} 在目标文件夹中已存在，是否覆盖？"):
                        return

                # 停止播放
                self.player.stop()
                time.sleep(0.1)  # 等待资源释放

                # 移动文件
                shutil.move(self.video_path, target_path)

                # 清理主应用中的相关状态
                if hasattr(self, 'main_app') and self.main_app:
                    try:
                        # 清理缩略图缓存
                        if self.video_path in self.main_app.thumbnail_cache:
                            del self.main_app.thumbnail_cache[self.video_path]

                        # 清理复选框状态
                        if self.video_path in self.main_app.checkbox_vars:
                            del self.main_app.checkbox_vars[self.video_path]

                        # 清理像素缓存
                        if self.video_path in self.main_app.pixel_cache:
                            del self.main_app.pixel_cache[self.video_path]

                        print(f"已清理主应用中的文件状态: {os.path.basename(self.video_path)}")
                    except Exception as cleanup_error:
                        print(f"清理主应用状态时出错: {cleanup_error}")

                # 更新移动历史记录
                if hasattr(self, 'move_history') and self.move_history is not None:
                    # 将新路径添加到历史记录开头
                    if target_folder in self.move_history:
                        self.move_history.remove(target_folder)
                    self.move_history.insert(0, target_folder)
                    # 只保留最近的20条记录
                    self.move_history = self.move_history[:20]

                    # 保存到文件
                    with open("move_history.txt", "w", encoding="utf-8") as f:
                        for item in self.move_history:
                            f.write(f"{item}\n")

                # 从视频列表中移除当前视频
                if self.video_list and self.video_path in self.video_list:
                    self.video_list.remove(self.video_path)
                    
                    # 如果还有其他视频，播放下一个
                    if self.video_list:
                        # 如果当前不是最后一个视频，保持当前索引
                        # 如果是最后一个视频，索引减1
                        if self.current_index >= len(self.video_list):
                            self.current_index = len(self.video_list) - 1
                        
                        # 先通知主窗口更新（移动文件后需要刷新预览页面）
                        if self.on_delete:
                            self.on_delete()

                        # 加载下一个视频
                        next_video = self.video_list[self.current_index]
                        if os.path.exists(next_video):
                            self.video_path = next_video
                            def show_message_and_load():
                                messagebox.showinfo("成功", f"文件已移动到:\n{target_path}\n正在播放下一个视频")
                                self.load_video(next_video)
                            self.window.after(0, show_message_and_load)
                            dialog.destroy()
                            return

                # 通知主窗口更新（移动文件后也需要刷新预览页面）
                if self.on_delete:
                    self.on_delete()

                # 如果没有下一个视频，关闭对话框和播放窗口
                dialog.destroy()
                self.close()
                messagebox.showinfo("成功", f"文件已移动到:\n{target_path}")

            except Exception as e:
                messagebox.showerror("错误", f"移动文件失败: {e}")

    def delete_video(self, show_confirm=True):
        """删除当前视频并播放下一个"""
        if not self.video_path:
            return
            
        try:
            if show_confirm:  # 只在需要时显示确认对话框
                if not messagebox.askyesno("确认删除", "确定要删除当前视频吗？"):
                    return
                    
            # 停止播放
            self.player.stop()
            time.sleep(0.1)  # 从0.1改为0.05秒

            # 删除文件 - 使用支持特殊字符的删除方法
            delete_file_with_system(self.video_path)

            # 从video_list中移除当前视频
            if self.video_list and self.video_path in self.video_list:
                self.video_list.remove(self.video_path)

            # 清理主应用中的相关状态
            if hasattr(self, 'main_app') and self.main_app:
                try:
                    # 清理缩略图缓存
                    if self.video_path in self.main_app.thumbnail_cache:
                        del self.main_app.thumbnail_cache[self.video_path]

                    # 清理复选框状态
                    if self.video_path in self.main_app.checkbox_vars:
                        del self.main_app.checkbox_vars[self.video_path]

                    # 清理像素缓存
                    if self.video_path in self.main_app.pixel_cache:
                        del self.main_app.pixel_cache[self.video_path]

                    print(f"已清理主应用中的文件状态: {os.path.basename(self.video_path)}")
                except Exception as cleanup_error:
                    print(f"清理主应用状态时出错: {cleanup_error}")

            # 通知主窗口更新
            if self.on_delete:
                self.window.after(0, self.on_delete)

            # 检查是否有下一个视频可以播放
            # 删除当前视频后，current_index 自然指向下一个视频，不需要 +1
            if self.video_list and self.current_index < len(self.video_list):
                next_video = self.video_list[self.current_index]
                if os.path.exists(next_video):  # 确保文件存在
                    self.video_path = next_video
                    self.load_video(next_video)  # 直接播放下一个视频,不显示提示
                    return

            # 如果当前索引超出范围，尝试播放前一个视频（如果删除的是最后一个视频）
            elif self.video_list and self.current_index > 0:
                self.current_index = len(self.video_list) - 1  # 指向最后一个视频
                next_video = self.video_list[self.current_index]
                if os.path.exists(next_video):
                    self.video_path = next_video
                    self.load_video(next_video)
                    return

            # 如果没有下一个视频，则关闭播放窗口
            self.close()

        except Exception as e:
            messagebox.showerror("错误", f"删除文件失败: {e}")

    def update_ui(self, video_path):
        """在主线程中更新UI"""
        try:
            if self.window.winfo_exists():  # 确保窗口还存在
                self.window.lift()
                self.window.focus_force()
                self.window.title(f"视频播放 - {os.path.basename(video_path)}")
        except Exception as e:
            print(f"更新UI失败: {e}")

    def load_video(self, video_path):
        """加载并播放视频"""
        def load_in_thread():
            try:
                # 停止当前播放
                if self.player.is_playing():
                    self.player.stop()
                    time.sleep(0.1)  # 等待资源释放

                media = self.instance.media_new(video_path)
                self.player.set_media(media)
                self.player.play()

                # 使用after方法在主线程中更新UI
                self.window.after(100, self.update_ui, video_path)
                
                # 更新复选框状态
                self.window.after(100, self.check_if_marked)

            except Exception as e:
                print(f"加载视频失败: {e}")
                # 在主线程中显示错误消息
                self.window.after(0, lambda: messagebox.showerror("错误", f"加载视频失败: {e}"))

        # 在新线程中加载视频
        threading.Thread(target=load_in_thread, daemon=True).start()

    def toggle_play(self):
        """切换播放/暂停状态"""
        if self.is_playing:
            self.player.pause()
            self.play_button.config(text="播放")
        else:
            self.player.play()
            self.play_button.config(text="暂停")
        self.is_playing = not self.is_playing

    def seek_video(self, value):
        """跳转到指定位置"""
        position = float(value) / 100.0
        self.player.set_position(position)

    def on_left_click(self, event):
        """左键点击后退10秒"""
        current_time = self.player.get_time()
        new_time = max(0, current_time - 10000)  # VLC使用毫秒
        self.player.set_time(new_time)

    def on_right_click(self, event):
        """右键点击前进10秒"""
        current_time = self.player.get_time()
        duration = self.player.get_length()
        new_time = min(duration, current_time + 10000)  # VLC使用毫秒
        self.player.set_time(new_time)

    def format_time(self, ms):
        """将毫秒转换为时:分:秒格式"""
        seconds = int(ms / 1000)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def update_time_display(self):
        """更新时间显示"""
        if self.player:
            current_time = self.player.get_time()
            duration = self.player.get_length()

            if current_time >= 0 and duration > 0:
                current_str = self.format_time(current_time)
                total_str = self.format_time(duration)
                self.time_label.config(text=f"{current_str} / {total_str}")

                # 更新进度条
                position = current_time / duration
                self.progress_var.set(position * 100)

        # 每100ms更新一次
        self.window.after(100, self.update_time_display)

    def close(self):
        """关闭播放器"""
        if self.player:
            self.player.stop()
            self.player.release()
        if self.instance:
            self.instance.release()
        self.window.destroy()

    def jump_backward(self):
        """向后跳转视频总时长的1/10"""
        duration = self.player.get_length()
        if duration > 0:
            jump_time = duration // 10  # 计算1/10的时长
            current_time = self.player.get_time()
            new_time = max(0, current_time - jump_time)
            self.player.set_time(new_time)

    def jump_forward(self):
        """向前跳转视频总时长的1/10"""
        duration = self.player.get_length()
        if duration > 0:
            jump_time = duration // 10  # 计算1/10的时长
            current_time = self.player.get_time()
            new_time = min(duration, current_time + jump_time)
            self.player.set_time(new_time)

    def next_frame(self):
        """播放下一帧"""
        if self.player:
            # 暂停播放
            self.player.pause()
            self.is_playing = False
            self.play_button.config(text="播放")
            
            # 获取当前帧率
            media = self.player.get_media()
            if media:
                media.parse()
                fps = media.get_fps()
                if fps > 0:
                    # 计算一帧的时长（毫秒）
                    frame_time = int(1000 / fps)
                    current_time = self.player.get_time()
                    # 前进一帧
                    new_time = min(self.player.get_length(), current_time + frame_time)
                    self.player.set_time(new_time)

    def prev_frame(self):
        """播放上一帧"""
        if self.player:
            # 暂停播放
            self.player.pause()
            self.is_playing = False
            self.play_button.config(text="播放")
            
            # 获取当前帧率
            media = self.player.get_media()
            if media:
                media.parse()
                fps = media.get_fps()
                if fps > 0:
                    # 计算一帧的时长（毫秒）
                    frame_time = int(1000 / fps)
                    current_time = self.player.get_time()
                    # 后退一帧
                    new_time = max(0, current_time - frame_time)
                    self.player.set_time(new_time)

    def jump_second_forward(self):
        """向前跳转1秒"""
        if self.player:
            current_time = self.player.get_time()
            duration = self.player.get_length()
            # 前进1000毫秒（1秒）
            new_time = min(duration, current_time + 1000)
            self.player.set_time(new_time)

    def jump_second_backward(self):
        """向后跳转1秒"""
        if self.player:
            current_time = self.player.get_time()
            # 后退1000毫秒（1秒）
            new_time = max(0, current_time - 1000)
            self.player.set_time(new_time)

    def check_if_marked(self):
        """检查当前视频在主界面是否被标记"""
        try:
            # 直接使用传递的主应用实例
            if hasattr(self, 'main_app'):
                app = self.main_app
                if hasattr(app, 'checkbox_vars') and self.video_path in app.checkbox_vars:
                    # 同步复选框状态
                    self.marked.set(app.checkbox_vars[self.video_path].get())
                else:
                    self.marked.set(False)
            else:
                # 尝试从窗口层次结构查找
                root = self.window.master
                if hasattr(root, 'videoPreviewer'):
                    app = root.videoPreviewer
                    if hasattr(app, 'checkbox_vars') and self.video_path in app.checkbox_vars:
                        self.marked.set(app.checkbox_vars[self.video_path].get())
                    else:
                        self.marked.set(False)
                else:
                    print("无法找到主应用实例")
                    self.marked.set(False)
        except Exception as e:
            print(f"检查标记状态失败: {e}")
            self.marked.set(False)

    def toggle_mark(self):
        """切换当前视频的标记状态"""
        try:
            # 直接使用传递的主应用实例
            if hasattr(self, 'main_app'):
                app = self.main_app
                if hasattr(app, 'checkbox_vars'):
                    if self.video_path in app.checkbox_vars:
                        # 同步到主应用的复选框
                        app.checkbox_vars[self.video_path].set(self.marked.get())
                    else:
                        # 如果主应用中不存在该变量，创建一个
                        var = tk.BooleanVar(value=self.marked.get())
                        app.checkbox_vars[self.video_path] = var
            else:
                # 尝试从窗口层次结构查找
                root = self.window.master
                if hasattr(root, 'videoPreviewer'):
                    app = root.videoPreviewer
                    if hasattr(app, 'checkbox_vars'):
                        if self.video_path in app.checkbox_vars:
                            app.checkbox_vars[self.video_path].set(self.marked.get())
                        else:
                            var = tk.BooleanVar(value=self.marked.get())
                            app.checkbox_vars[self.video_path] = var
                else:
                    print("无法找到主应用实例")
        except Exception as e:
            print(f"切换标记状态失败: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoPreviewer(root)
    # 将app实例设为root的属性
    root.videoPreviewer = app
    root.geometry("1600x900+150+50")  # 进一步增加窗口宽度以显示所有按钮
    root.mainloop()

