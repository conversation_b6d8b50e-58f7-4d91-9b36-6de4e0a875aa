import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import math
import win32com.client
import json
import gc
import cv2
import numpy as np
import shutil
import warnings

# 配置PIL以处理大图片
Image.MAX_IMAGE_PIXELS = None  # 移除像素限制，允许处理超大图片
# 过滤PIL的DecompressionBombWarning警告
warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)

class ImagePopup(tk.Toplevel):
    """单独的图片查看窗口"""
    def __init__(self, parent, image_path, image_paths, viewer):
        """初始化图片预览窗口"""
        super().__init__(parent)
        
        self.parent = parent
        self.viewer = viewer
        # 创建图片路径列表的副本，避免与主窗口共享引用
        self.image_paths = image_paths.copy()
        self.current_image_path = image_path
        self.current_index = self.image_paths.index(image_path)
        
        self.title("图片查看")
        
        # 去掉窗口装饰（标题栏除外）
        self.resizable(False, False)
        
        # 创建一个不透明的背景遮罩
        self.configure(bg='black')
        self.attributes('-alpha', 1)
        
        # 创建主框架
        self.main_frame = ttk.Frame(self)
        self.main_frame.pack(expand=True, fill=tk.BOTH)
        
        # 创建图片显示标签
        self.image_label = tk.Label(self.main_frame, bd=0, highlightthickness=0, bg='black')
        self.image_label.pack(expand=True)
        
        # 加载并显示图片
        self.load_current_image()
        
        # 绑定键盘事件
        self.bind('<KeyPress-Escape>', lambda e: self.destroy())
        self.bind('<KeyPress-Left>', lambda e: self.prev_image())
        self.bind('<KeyPress-Right>', lambda e: self.next_image())
        self.bind('<KeyPress-Delete>', lambda e: self.delete_current_image())
        
        # 绑定鼠标事件
        self.image_label.bind('<MouseWheel>', self.on_mousewheel)
        self.bind('<Button-1>', lambda e: self.destroy())
        self.main_frame.bind('<Button-1>', lambda e: self.destroy())
        self.image_label.bind('<Button-1>', lambda e: self.destroy())
        
        # 确保窗口可以接收键盘事件
        self.focus_force()
        
        # 窗口居中显示
        self.center_window()
        
        # 将此窗口设为模态窗口，阻止与主窗口的交互
        self.grab_set()
        
    def load_current_image(self):
        """加载当前图片"""
        try:
            with Image.open(self.image_paths[self.current_index]) as img:
                # 获取屏幕尺寸
                screen_width = self.winfo_screenwidth()
                screen_height = self.winfo_screenheight()

                # 获取图片原始尺寸
                original_width, original_height = img.size

                # 如果图片尺寸超过屏幕90%，则等比缩放
                max_width = int(screen_width * 0.9)
                max_height = int(screen_height * 0.9)

                if original_width > max_width or original_height > max_height:
                    scale = min(max_width/original_width, max_height/original_height)
                    new_size = (int(original_width * scale), int(original_height * scale))
                    img = img.resize(new_size, Image.Resampling.LANCZOS)

                # 创建PhotoImage
                photo = ImageTk.PhotoImage(img)
                self.image_label.configure(image=photo)
                self.image_label.image = photo

                # 重新绑定鼠标滚轮事件（确保删除图片后滚轮仍然有效）
                self.rebind_mouse_events()

                # 调整窗口大小以适应图片
                self.geometry(f"{photo.width()}x{photo.height()}")

                # 获取文件名并显示在窗口标题栏
                file_name = os.path.basename(self.image_paths[self.current_index])
                self.title(file_name)

                # 重新居中窗口
                self.center_window()

        except Exception as e:
            print(f"无法加载图片 {self.image_paths[self.current_index]}: {str(e)}")

    def rebind_mouse_events(self):
        """重新绑定鼠标事件，确保删除图片后事件仍然有效"""
        # 重新绑定鼠标滚轮事件
        self.image_label.bind('<MouseWheel>', self.on_mousewheel)

        # 重新绑定点击事件
        self.image_label.bind('<Button-1>', lambda e: self.destroy())

        # 重新绑定键盘事件
        self.bind('<KeyPress-Escape>', lambda e: self.destroy())
        self.bind('<KeyPress-Left>', lambda e: self.prev_image())
        self.bind('<KeyPress-Right>', lambda e: self.next_image())
        self.bind('<KeyPress-Delete>', lambda e: self.delete_current_image())

        # 确保窗口可以接收键盘事件
        self.focus_force()

    def format_file_size(self, size_in_bytes):
        """格式化文件大小显示"""
        if size_in_bytes == 0:
            return "0 B"
            
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_in_bytes < 1024.0:
                return f"{size_in_bytes:.1f} {unit}"
            size_in_bytes /= 1024.0
        return f"{size_in_bytes:.1f} TB"
    
    def center_window(self):
        """将窗口居中显示"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'+{x}+{y}')
    
    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        if event.delta > 0:
            self.prev_image()
        else:
            self.next_image()
    
    def prev_image(self):
        """显示上一张图片"""
        if not self.image_paths:  # 如果没有图片，直接返回
            return
        if self.current_index > 0:
            self.current_index -= 1
            self.load_current_image()
    
    def next_image(self):
        """显示下一张图片"""
        if not self.image_paths:  # 如果没有图片，直接返回
            return
        if self.current_index < len(self.image_paths) - 1:
            self.current_index += 1
            self.load_current_image()
    
    def check_click(self, event):
        """检查点击位置，如果在图片外则关闭窗口"""
        if event.widget == self:
            self.destroy()
    
    def delete_current_image(self, event=None):
        """删除当前显示的图片"""
        try:
            if not self.image_paths:
                self.destroy()
                return

            # 获取当前图片路径和索引
            current_path = self.current_image_path
            current_index = self.current_index

            if not current_path:
                self.destroy()
                return

            # 先删除文件（只删除文件，不刷新主窗口）
            try:
                # 规范化路径
                norm_path = os.path.normpath(current_path)
                # 使用重试机制删除文件
                self.viewer._delete_file_with_retry(norm_path)
                # 通知主窗口更新（但不刷新显示）
                self.viewer.remove_from_lists(norm_path)
            except Exception as e:
                messagebox.showerror("错误", f"删除图片失败：{str(e)}")
                return  # 删除失败，不继续处理

            # 从当前窗口的列表中移除当前图片
            if current_path in self.image_paths:
                self.image_paths.remove(current_path)

            # 如果没有更多图片了，直接关闭窗口
            if not self.image_paths:
                self.destroy()
                return

            # 调整索引并加载下一张图片
            # 如果删除的是最后一张图片，显示前一张
            if current_index >= len(self.image_paths):
                self.current_index = len(self.image_paths) - 1
            self.current_image_path = self.image_paths[self.current_index]
            self.load_current_image()

        except Exception as e:
            messagebox.showerror("错误", f"操作失败：{str(e)}")
            self.destroy()

class ImageViewer:
    def __init__(self, initial_folder=None):
        self.root = tk.Tk()
        self.root.title("图片预览器")
        self.root.geometry("1200x800")

        # 保存初始文件夹路径
        self.initial_folder = initial_folder
        
        # 添加选择状态跟踪
        self.selected_files = set()  # 存储被选中的文件路径
        self.selection_mode = False  # 是否处于多选模式
        self.shift_pressed = False  # 跟踪Shift键状态
        self.last_selected = None  # 记录最后选择的项目
        
        # 添加排序相关变量
        self.sort_by_var = tk.StringVar(value="name")  # 排序方式：name（文件名）或time（时间）
        self.sort_order_var = tk.StringVar(value="asc")  # 排序顺序：asc（升序）或desc（降序）
        
        # 添加文件大小过滤变量
        self.size_filter_enabled = tk.BooleanVar(value=False)  # 是否启用大小过滤
        self.size_filter_mode_var = tk.StringVar(value="below")  # 以上/以下选择
        self.size_threshold = 300 * 1024  # 300KB的阈值（字节）
        
        self.supported_formats = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.mp4', '.avi', '.mkv', '.mov', '.wmv')
        self.video_formats = ('.mp4', '.avi', '.mkv', '.mov', '.wmv')  # 视频格式列表
        self.thumbnail_size = (200, 200)  # 增大缩略图尺寸
        self.current_folder = None
        self.thumbnails = []
        self.image_paths = []
        self.all_image_paths = []  # 存储所有图片路径，用于过滤
        
        # 分辨率过滤相关变量
        self.resolution_filter_var = tk.StringVar(value="none")  # 单选变量
        self.filter_mode_var = tk.StringVar(value="below")  # 以上/以下选择
        self.resolution_filters = [
            {"name": "不过滤", "value": "none", "pixels": float('inf')},
            {"name": "50万像素", "value": "500000", "pixels": 500000},
            {"name": "100万像素", "value": "1000000", "pixels": 1000000},
            {"name": "200万像素", "value": "2000000", "pixels": 2000000},
            {"name": "400万像素", "value": "4000000", "pixels": 4000000}
        ]
        self.resolution_cache = {}  # 缓存图片分辨率，避免重复读取
        
        # 移除配置文件和历史记录功能
        
        # 分页相关变量
        self.current_page = 1
        self.items_per_page = 12  # 每页显示的图片数量
        
        # 添加窗口尺寸跟踪变量
        self.last_frame_width = 0
        self.last_frame_height = 0
        
        # 添加网格布局跟踪变量
        self.current_rows = 0
        self.current_cols = 0
        
        # 添加加载状态标志
        self.is_loading = False
        
        # 图片加载限制
        self.max_concurrent_loads = 5  # 最大同时加载的图片数量
        self.loading_queue = []  # 图片加载队列
        self.active_loads = 0  # 当前活跃的加载任务数
        self.load_lock = threading.Lock()  # 线程锁，用于保护并发访问
        
        # 设置文件夹名称最大显示长度
        self.max_folder_name_length = 30
        
        self.setup_ui()

        # 绑定关闭窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 定期检查图片加载队列
        self.root.after(100, self.process_loading_queue)

        # 如果有初始文件夹，自动打开它
        if self.initial_folder and os.path.exists(self.initial_folder):
            # 延迟一点时间确保界面完全初始化
            self.root.after(200, lambda: self.open_folder(self.initial_folder))



    def format_file_size(self, size_in_bytes):
        """格式化文件大小显示"""
        if size_in_bytes == 0:
            return "0 B"
            
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_in_bytes < 1024.0:
                return f"{size_in_bytes:.1f} {unit}"
            size_in_bytes /= 1024.0
        return f"{size_in_bytes:.1f} TB"
    
    def format_folder_path(self, folder_path):
        """智能格式化文件夹路径，确保显示固定长度"""
        if not folder_path:
            return "未选择文件夹"
            
        # 获取驱动器部分（如 "D:"）和剩余路径
        drive, path = os.path.splitdrive(folder_path)
        
        # 将路径分割成各个部分
        parts = path.split(os.sep)
        parts = [p for p in parts if p]  # 移除空字符串
        
        if not parts:
            return drive + os.sep if drive else "根目录"
            
        # 如果路径很短，直接显示
        full_path = drive + os.sep + os.sep.join(parts)
        if len(full_path) <= self.max_folder_name_length:
            return full_path
            
        # 基本部分的长度（驱动器和必要的分隔符）
        base_length = len(drive + os.sep)
        
        # 最小显示长度
        min_show_length = 5
        
        # 获取最后一个文件夹名和其余部分
        last_part = parts[-1]
        middle_parts = parts[:-1] if len(parts) > 1 else []
        
        # 计算总的可用空间
        available_space = self.max_folder_name_length - base_length
        if middle_parts:
            available_space -= 3  # 为省略号预留空间
            
        # 如果没有中间部分
        if not middle_parts:
            if len(last_part) > available_space:
                return f"{drive}{os.sep}{last_part[:available_space-1]}…"
            return f"{drive}{os.sep}{last_part}"
            
        # 计算每部分的目标长度
        target_last_length = min(len(last_part), max(min_show_length, available_space // 3))
        target_middle_length = available_space - target_last_length
        
        # 处理最后一部分
        if len(last_part) > target_last_length:
            last_part = last_part[:target_last_length-1] + "…"
            
        # 处理中间部分
        middle_text = ""
        space_per_part = max(min_show_length, target_middle_length // len(middle_parts))
        
        for part in middle_parts:
            if len(part) > space_per_part:
                middle_text += part[:space_per_part-1] + "…" + os.sep
            else:
                middle_text += part + os.sep
                
        # 移除最后多余的分隔符并添加省略号
        middle_text = middle_text.rstrip(os.sep)
        
        # 组合最终结果
        result = f"{drive}{os.sep}{middle_text}...{last_part}"
        
        # 确保不超过最大长度
        if len(result) > self.max_folder_name_length:
            excess = len(result) - self.max_folder_name_length
            last_part = last_part[:-excess-1] + "…"
            result = f"{drive}{os.sep}{middle_text}...{last_part}"
            
        return result
    
    def setup_ui(self):
        # 绑定Shift键事件
        self.root.bind('<KeyPress-Shift_L>', self.on_shift_press)
        self.root.bind('<KeyRelease-Shift_L>', self.on_shift_release)
        self.root.bind('<KeyPress-Shift_R>', self.on_shift_press)
        self.root.bind('<KeyRelease-Shift_R>', self.on_shift_release)

        # 绑定上下键切换文件夹
        self.root.bind('<KeyPress-Up>', self.on_up_key)
        self.root.bind('<KeyPress-Down>', self.on_down_key)

        # 创建主框架（移除分割窗口）
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 添加顶部信息栏
        self.info_frame = ttk.Frame(self.main_frame)
        self.info_frame.pack(fill=tk.X, padx=10, pady=5)

        # 添加选择文件夹按钮
        self.select_folder_btn = ttk.Button(self.info_frame, text="选择文件夹", command=self.select_folder)
        self.select_folder_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 创建文件夹信息标签（使用ttk.Label）
        self.folder_info = ttk.Label(self.info_frame, text="当前文件夹: 未选择文件夹", anchor="w")
        self.folder_info.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 为文件夹信息标签添加鼠标悬停提示
        def show_full_path(event):
            if self.current_folder:
                self.folder_info.configure(text=f"当前文件夹: {self.current_folder}")
                
        def restore_short_path(event):
            if self.current_folder:
                formatted_path = self.format_folder_path(self.current_folder)
                self.folder_info.configure(text=f"当前文件夹: {formatted_path}")
        
        self.folder_info.bind('<Enter>', show_full_path)
        self.folder_info.bind('<Leave>', restore_short_path)
        
        # 添加批量操作按钮（移到右侧）
        self.batch_frame = ttk.Frame(self.info_frame)
        self.batch_frame.pack(side=tk.RIGHT, padx=(0, 10))
        
        # 图片总数信息标签
        self.total_info = ttk.Label(self.info_frame, text="0/0")
        self.total_info.pack(side=tk.RIGHT)
        
        # 全选按钮
        self.select_all_btn = ttk.Button(self.batch_frame, text="全选", command=self.select_all)
        self.select_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 取消全选按钮
        self.deselect_all_btn = ttk.Button(self.batch_frame, text="取消全选", command=self.deselect_all)
        self.deselect_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 批量删除按钮
        self.batch_delete_btn = ttk.Button(self.batch_frame, text="删除所选", command=self.delete_selected)
        self.batch_delete_btn.pack(side=tk.LEFT, padx=5)
        
        # 批量移动按钮
        self.batch_move_btn = ttk.Button(self.batch_frame, text="移动所选", command=self.move_selected)
        self.batch_move_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加排序控制框架
        self.sort_frame = ttk.LabelFrame(self.main_frame, text="排序方式")
        self.sort_frame.pack(fill=tk.X, padx=10, pady=(5, 0))
        
        # 排序方式选项
        ttk.Radiobutton(
            self.sort_frame,
            text="按文件名",
            variable=self.sort_by_var,
            value="name",
            command=self.apply_sort
        ).grid(row=0, column=0, padx=10, pady=5)
        
        ttk.Radiobutton(
            self.sort_frame,
            text="按时间",
            variable=self.sort_by_var,
            value="time",
            command=self.apply_sort
        ).grid(row=0, column=1, padx=10, pady=5)
        
        # 添加分隔线
        ttk.Separator(self.sort_frame, orient='vertical').grid(row=0, column=2, padx=10, sticky='ns')
        
        # 排序顺序选项
        ttk.Radiobutton(
            self.sort_frame,
            text="升序",
            variable=self.sort_order_var,
            value="asc",
            command=self.apply_sort
        ).grid(row=0, column=3, padx=10, pady=5)
        
        ttk.Radiobutton(
            self.sort_frame,
            text="降序",
            variable=self.sort_order_var,
            value="desc",
            command=self.apply_sort
        ).grid(row=0, column=4, padx=10, pady=5)
        
        # 添加分辨率过滤框架
        self.filter_frame = ttk.LabelFrame(self.main_frame, text="分辨率过滤")
        self.filter_frame.pack(fill=tk.X, padx=10, pady=(5, 0))
        
        # 将所有控件放在同一行
        # 创建分辨率过滤选项（单选按钮）
        for i, filter_option in enumerate(self.resolution_filters):
            rb = ttk.Radiobutton(
                self.filter_frame, 
                text=filter_option["name"],
                variable=self.resolution_filter_var,
                value=filter_option["value"],
                command=self.apply_resolution_filter
            )
            rb.grid(row=0, column=i, padx=10, pady=5)
        
        # 创建分隔线
        separator = ttk.Separator(self.filter_frame, orient='vertical')
        separator.grid(row=0, column=len(self.resolution_filters), padx=10, sticky='ns')
        
        # 创建以上/以下选择（放在同一行）
        ttk.Radiobutton(
            self.filter_frame, 
            text="以下",
            variable=self.filter_mode_var,
            value="below",
            command=self.apply_resolution_filter
        ).grid(row=0, column=len(self.resolution_filters)+1, padx=10, pady=5)
        
        ttk.Radiobutton(
            self.filter_frame, 
            text="以上",
            variable=self.filter_mode_var,
            value="above",
            command=self.apply_resolution_filter
        ).grid(row=0, column=len(self.resolution_filters)+2, padx=10, pady=5)
        
        # 添加文件大小过滤框架
        self.size_filter_frame = ttk.LabelFrame(self.main_frame, text="文件大小过滤")
        self.size_filter_frame.pack(fill=tk.X, padx=10, pady=(5, 0))
        
        # 创建文件大小过滤控件
        ttk.Checkbutton(
            self.size_filter_frame,
            text="300KB",
            variable=self.size_filter_enabled,
            command=self.apply_filters
        ).grid(row=0, column=0, padx=10, pady=5)
        
        # 创建以上/以下选择
        ttk.Radiobutton(
            self.size_filter_frame,
            text="以下",
            variable=self.size_filter_mode_var,
            value="below",
            command=self.apply_filters
        ).grid(row=0, column=1, padx=10, pady=5)
        
        ttk.Radiobutton(
            self.size_filter_frame,
            text="以上",
            variable=self.size_filter_mode_var,
            value="above",
            command=self.apply_filters
        ).grid(row=0, column=2, padx=10, pady=5)
        
        # 添加分隔线
        self.separator = ttk.Separator(self.main_frame, orient='horizontal')
        self.separator.pack(fill=tk.X, padx=5, pady=2)
        
        # 创建带滚动条的画布
        self.canvas = tk.Canvas(self.main_frame)
        self.scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=self.canvas.yview)
        self.thumbnail_frame = ttk.Frame(self.canvas)
        
        # 配置画布滚动
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局滚动组件
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=(5, 0))
        
        # 创建画布上的窗口
        self.canvas_frame = self.canvas.create_window((0, 0), window=self.thumbnail_frame, anchor="nw")
        
        # 底部控制区域
        self.control_frame = ttk.Frame(self.root)
        self.control_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)
        
        # 创建内部框架用于居中按钮
        self.button_frame = ttk.Frame(self.control_frame)
        self.button_frame.pack(anchor=tk.CENTER)
        
        # 分页控制
        self.prev_button = ttk.Button(self.button_frame, text="上一页", command=self.prev_page)
        self.prev_button.pack(side=tk.LEFT, padx=5)
        
        # 页面跳转控制
        self.jump_frame = ttk.Frame(self.button_frame)
        self.jump_frame.pack(side=tk.LEFT, padx=5)
        
        self.page_label = ttk.Label(self.jump_frame, text="第")
        self.page_label.pack(side=tk.LEFT)
        
        self.page_var = tk.StringVar()
        self.page_entry = ttk.Entry(self.jump_frame, textvariable=self.page_var, width=5)
        self.page_entry.pack(side=tk.LEFT, padx=2)
        
        self.total_label = ttk.Label(self.jump_frame, text="/1页")
        self.total_label.pack(side=tk.LEFT)
        
        self.jump_button = ttk.Button(self.jump_frame, text="跳转", command=self.jump_to_page)
        self.jump_button.pack(side=tk.LEFT, padx=2)
        
        self.next_button = ttk.Button(self.button_frame, text="下一页", command=self.next_page)
        self.next_button.pack(side=tk.LEFT, padx=5)
        
        # 添加删除所有按钮
        self.delete_all_button = ttk.Button(self.button_frame, text="删除所有", command=self.delete_all_images)
        self.delete_all_button.pack(side=tk.LEFT, padx=5)



        # 添加删除文件夹按钮（放在最右边，使用红色，与其他按钮保持距离）
        self.delete_folder_button = tk.Button(self.button_frame, text="删除文件夹", command=self.delete_current_folder,
                                            bg='red', fg='white', font=('Arial', 9, 'bold'),
                                            activebackground='darkred', activeforeground='white')
        self.delete_folder_button.pack(side=tk.RIGHT, padx=(100, 5))
        
        # 绑定回车键到跳转功能
        self.page_entry.bind('<Return>', lambda e: self.jump_to_page())
        
        # 绑定事件
        self.thumbnail_frame.bind('<Configure>', self.on_frame_configure)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.bind_mousewheel_events()
        self.canvas.bind('<Enter>', lambda e: self.canvas.focus_set())

    def bind_mousewheel_events(self):
        """绑定鼠标滚轮事件，确保删除图片后滚轮仍然有效"""
        # 简化绑定，只绑定到主要控件，避免冲突
        self.canvas.bind('<MouseWheel>', self.on_mousewheel)
        self.thumbnail_frame.bind('<MouseWheel>', self.on_mousewheel)

        # 为了确保在所有子控件上都能滚动，使用递归绑定
        self._bind_mousewheel_recursive(self.thumbnail_frame)

    def _bind_mousewheel_recursive(self, widget):
        """递归绑定鼠标滚轮事件到所有子控件"""
        try:
            # 绑定到当前控件
            widget.bind('<MouseWheel>', self.on_mousewheel)

            # 递归绑定到所有子控件
            for child in widget.winfo_children():
                self._bind_mousewheel_recursive(child)
        except tk.TclError:
            # 如果控件已被销毁，忽略错误
            pass

    def delete_all_images(self):
        """删除当前文件夹中所有显示的图片（不包括视频文件）"""
        if not self.image_paths:
            messagebox.showinfo("提示", "没有可删除的图片")
            return

        # 过滤出只有图片文件（排除视频文件）
        image_only_paths = []
        for path in self.image_paths:
            file_ext = os.path.splitext(path)[1].lower()
            if file_ext not in self.video_formats:  # 排除视频格式
                image_only_paths.append(path)

        if not image_only_paths:
            messagebox.showinfo("提示", "没有可删除的图片文件（视频文件不会被删除）")
            return

        # 确认是否删除所有图片
        if not messagebox.askyesno("确认删除", f"确定要删除当前显示的所有 {len(image_only_paths)} 张图片吗？\n（视频文件不会被删除）\n此操作无法撤销！"):
            return

        # 记录删除失败的文件
        failed_files = []
        deleted_count = 0

        # 删除所有图片（排除视频）
        for path in image_only_paths[:]:  # 使用副本进行迭代，避免修改迭代中的列表
            try:
                # 规范化路径
                norm_path = os.path.normpath(path)

                # 使用重试机制删除文件
                self._delete_file_with_retry(norm_path)

                # 从列表中移除
                if norm_path in self.image_paths:
                    self.image_paths.remove(norm_path)
                if norm_path in self.all_image_paths:
                    self.all_image_paths.remove(norm_path)

                deleted_count += 1
            except Exception as e:
                failed_files.append(f"{os.path.basename(path)}: {str(e)}")

        # 智能调整当前页面
        self.adjust_current_page_after_deletion()

        # 刷新显示（保持在当前页面）
        self.load_current_page()
        self.update_page_controls()



        # 显示结果消息
        if failed_files:
            messagebox.showwarning("删除结果", f"成功删除 {deleted_count} 张图片，{len(failed_files)} 张图片删除失败。\n\n失败列表:\n" + "\n".join(failed_files[:10]) + ("..." if len(failed_files) > 10 else ""))
        else:
            messagebox.showinfo("删除成功", f"成功删除所有 {deleted_count} 张图片")

    def delete_current_folder(self):
        """删除当前文件夹"""
        if not self.current_folder:
            messagebox.showinfo("提示", "没有选择文件夹")
            return

        # 添加确认对话框
        folder_name = os.path.basename(self.current_folder)
        if not messagebox.askyesno("确认删除", f"确定要删除文件夹 '{folder_name}' 吗？\n\n此操作将删除文件夹中的所有内容，无法撤销！", icon='warning'):
            return

        try:
            # 记录删除的文件夹路径到指定文件
            deleted_index_path = r"z:\work\pic_deleted_index.txt"
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(deleted_index_path), exist_ok=True)

                # 获取当前时间
                import datetime
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 追加记录到文件
                with open(deleted_index_path, "a", encoding="utf-8") as f:
                    f.write(f"{current_time} | {self.current_folder}\n")
            except Exception as e:
                print(f"记录删除文件夹失败: {str(e)}")

            # 删除文件夹
            import shutil
            shutil.rmtree(self.current_folder)

            # 获取父文件夹
            parent_folder = os.path.dirname(self.current_folder)

            # 查找包含图片的文件夹
            folder_with_images = self.find_first_folder_with_images(parent_folder)
            self.current_folder = folder_with_images if folder_with_images else parent_folder

            # 重新加载图片
            self.load_images()

            messagebox.showinfo("删除成功", f"文件夹 '{folder_name}' 已删除")

        except Exception as e:
            messagebox.showerror("错误", f"删除文件夹失败：{str(e)}")

    def move_current_folder(self):
        """移动当前文件夹"""
        if not self.current_folder:
            messagebox.showinfo("提示", "没有选择文件夹")
            return

        # 直接打开文件夹选择对话框
        target_path = filedialog.askdirectory(title='选择移动目标文件夹')

        # 如果没有选择目标路径，直接返回
        if not target_path:
            return

        try:
            # 获取原文件夹路径和名称
            folder_path = self.current_folder
            folder_name = os.path.basename(folder_path)
            parent_path = os.path.dirname(folder_path)

            # 构建目标路径
            target_full_path = os.path.join(target_path, folder_name)

            # 如果目标路径已存在，自动重命名
            if os.path.exists(target_full_path):
                folder_name = self._generate_unique_filename(target_path, folder_name)
                target_full_path = os.path.join(target_path, folder_name)

            # 获取兄弟文件夹列表，用于移动后定位
            siblings = []
            try:
                # 直接从文件系统获取兄弟文件夹
                for item in os.listdir(parent_path):
                    item_path = os.path.join(parent_path, item)
                    if os.path.isdir(item_path) and item_path != folder_path:
                        siblings.append(item_path)
                siblings.sort()  # 按字母顺序排序

                # 找到当前文件夹在列表中的位置，获取后续文件夹
                folder_name = os.path.basename(folder_path)
                try:
                    current_index = [os.path.basename(s) for s in siblings].index(folder_name)
                    # 如果找到了，移除当前文件夹并保留后续的
                    siblings = siblings[current_index:]
                except ValueError:
                    # 如果没找到，保留所有兄弟文件夹
                    pass
            except Exception as e:
                print(f"获取兄弟文件夹失败: {str(e)}")
                siblings = []

            # 移动文件夹
            import shutil
            shutil.move(folder_path, target_full_path)

            # 不需要完全刷新目录树，只需要局部更新
            # self.populate_root_drives()  # 移除这个会导致闪烁的操作

            # 定位逻辑：与右键菜单保持一致
            # 如果有兄弟目录，定位到下一个兄弟目录
            if siblings:
                next_path = siblings[0]
                # 查找包含图片的文件夹
                folder_with_images = self.find_first_folder_with_images(next_path)
                self.current_folder = folder_with_images if folder_with_images else next_path
            else:
                # 如果没有兄弟目录，定位到父目录
                folder_with_images = self.find_first_folder_with_images(parent_path)
                self.current_folder = folder_with_images if folder_with_images else parent_path

            # 加载图片
            self.load_images()

        except Exception as e:
            messagebox.showerror("错误", f"移动文件夹失败：{str(e)}")



    
    def on_frame_configure(self, event=None):
        """更新画布的滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def on_canvas_configure(self, event):
        """当画布大小改变时，调整内部框架的宽度"""
        self.canvas.itemconfig(self.canvas_frame, width=event.width)
        
    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        try:
            # 简化处理逻辑，直接滚动画布
            # 计算滚动量，Windows下event.delta通常是120的倍数
            scroll_amount = int(-1 * (event.delta / 120))

            # 执行滚动
            self.canvas.yview_scroll(scroll_amount, "units")

            # 阻止事件继续传播，避免重复处理
            return "break"
        except Exception as e:
            # 如果出现错误，静默处理，不影响用户体验
            pass

    def calculate_grid(self):
        """计算网格布局"""
        frame_width = self.canvas.winfo_width()
        frame_height = self.canvas.winfo_height()
        
        # 如果尺寸没有实质变化，返回当前的行列数
        if (abs(frame_width - self.last_frame_width) < 10 and 
            abs(frame_height - self.last_frame_height) < 10):
            return self.current_rows, self.current_cols
        
        # 更新记录的尺寸
        self.last_frame_width = frame_width
        self.last_frame_height = frame_height
        
        # 固定每页显示60个图片，计算合适的列数
        self.items_per_page = 60
        cols = min(10, max(4, frame_width // (self.thumbnail_size[0] + 20)))
        rows = math.ceil(self.items_per_page / cols)
        
        # 保存当前的行列数
        self.current_rows = rows
        self.current_cols = cols
        
        return rows, cols
    
    def load_current_page(self):
        """加载当前页的图片"""
        # 如果正在加载中，则返回
        if self.is_loading:
            return
            
        self.is_loading = True
        
        # 清除现有缩略图
        for widget in self.thumbnail_frame.winfo_children():
            widget.destroy()
        self.thumbnails.clear()
        
        # 清空加载队列
        with self.load_lock:
            self.loading_queue.clear()
            self.active_loads = 0
        
        # 将滚动条定位到顶部
        self.canvas.yview_moveto(0)
        
        if not self.image_paths:
            self.is_loading = False
            return
            
        # 计算网格布局
        rows, cols = self.calculate_grid()
        old_items_per_page = self.items_per_page
        new_items_per_page = rows * cols
        
        # 如果每页显示数量没有变化，且不是第一次加载，则不重新加载
        if (hasattr(self, 'items_per_page') and 
            old_items_per_page == new_items_per_page and 
            len(self.thumbnail_frame.winfo_children()) > 0):
            self.is_loading = False
            return
            
        self.items_per_page = new_items_per_page  # 修正变量名
        
        # 计算当前页的图片范围
        start_idx = (self.current_page - 1) * self.items_per_page
        end_idx = min(start_idx + self.items_per_page, len(self.image_paths))
        
        # 创建当前页的缩略图
        self.create_thumbnails(self.image_paths[start_idx:end_idx], cols)

        # 重新绑定鼠标滚轮事件，确保删除图片后滚轮仍然有效
        self.bind_mousewheel_events()

        # 更新页码显示
        total_pages = max(1, math.ceil(len(self.image_paths) / self.items_per_page))
        self.total_label.configure(text=f"/{total_pages}页")
        self.page_var.set(str(self.current_page))
    
    def create_thumbnails(self, paths, cols):
        """创建缩略图（添加到加载队列）"""
        # 创建占位框架
        for idx, path in enumerate(paths):
            # 计算行列位置
            row = idx // cols
            col = idx % cols
            
            # 创建占位框架
            frame = ttk.Frame(self.thumbnail_frame)
            frame.grid(row=row, column=col, padx=5, pady=5)
            
            # 添加加载中标签
            loading_label = ttk.Label(frame, text="加载中...", width=15, anchor="center")
            loading_label.pack(pady=self.thumbnail_size[1]//2)
            
            # 将图片路径添加到加载队列
            with self.load_lock:
                self.loading_queue.append((path, frame, row, col))
        
        # 启动队列处理
        self.process_loading_queue()
        
        # 设置加载完成
        self.is_loading = False
    
    def process_loading_queue(self):
        """处理图片加载队列"""
        # 检查应用程序是否仍在运行
        try:
            if not self.root.winfo_exists():
                return  # 如果根窗口已被销毁，停止处理
            
            # 检查是否有等待加载的图片，并且当前活跃加载数小于最大值
            with self.load_lock:
                if self.loading_queue and self.active_loads < self.max_concurrent_loads:
                    # 获取下一个要加载的图片
                    path_data = self.loading_queue.pop(0)
                    self.active_loads += 1
                    
                    # 使用守护线程加载图片，确保主程序退出时线程也会退出
                    thread = threading.Thread(
                        target=self.load_image_thumbnail, 
                        args=(path_data,),
                        daemon=True
                    )
                    thread.start()
            
            # 继续检查队列，使用after_idle确保在主线程空闲时执行
            if self.root.winfo_exists():
                self.root.after(100, self.process_loading_queue)
                
        except (tk.TclError, RuntimeError) as e:
            # 如果发生错误，可能是应用程序正在关闭
            print(f"停止处理图片加载队列: {str(e)}")
            return
        except Exception as e:
            # 捕获其他异常，但继续尝试处理队列
            print(f"处理加载队列时出错: {str(e)}")
            if self.root.winfo_exists():
                self.root.after(100, self.process_loading_queue)
    
    def load_image_thumbnail(self, path_data):
        """在后台线程中加载图片或视频缩略图"""
        path, frame, row, col = path_data
        try:
            # 检查当前页面是否已经改变
            current_start_idx = (self.current_page - 1) * self.items_per_page
            current_end_idx = min(current_start_idx + self.items_per_page, len(self.image_paths))
            current_paths = self.image_paths[current_start_idx:current_end_idx] if self.image_paths else []
            
            # 如果当前路径不在当前页面显示的文件中，则不需要更新UI
            if path not in current_paths:
                with self.load_lock:
                    self.active_loads -= 1
                return
            
            # 检查是否是视频文件
            if os.path.splitext(path)[1].lower() in self.video_formats:
                try:
                    # 使用OpenCV读取视频第一帧
                    cap = cv2.VideoCapture(path)
                    if cap.isOpened():
                        ret, frame_data = cap.read()
                        cap.release()
                        
                        if ret:
                            # 将BGR转换为RGB
                            frame_data = cv2.cvtColor(frame_data, cv2.COLOR_BGR2RGB)
                            # 创建PIL图像
                            img = Image.fromarray(frame_data)
                            # 创建缩略图
                            img.thumbnail(self.thumbnail_size)
                            
                            # 保存处理后的图像数据为字节流
                            import io
                            buffer = io.BytesIO()
                            img.save(buffer, format="PNG")
                            img_data = buffer.getvalue()
                            
                            # 在主线程中安全地创建PhotoImage并更新UI
                            if hasattr(self.root, 'winfo_exists') and self.root.winfo_exists():
                                self.root.after_idle(lambda: self.create_video_thumbnail(img_data, path, frame, row, col))
                        else:
                            raise Exception("无法读取视频帧")
                    else:
                        raise Exception("无法打开视频文件")
                except Exception as e:
                    print(f"处理视频文件时出错 {path}: {str(e)}")
                    if hasattr(self.root, 'winfo_exists') and self.root.winfo_exists():
                        self.root.after_idle(lambda: self.update_thumbnail_error(path, frame, row, col))
            else:
                # 原有的图片处理逻辑
                try:
                    with Image.open(path) as img:
                        img.thumbnail(self.thumbnail_size)
                        import io
                        buffer = io.BytesIO()
                        img.save(buffer, format=img.format or "PNG")
                        img_data = buffer.getvalue()
                        
                        if hasattr(self.root, 'winfo_exists') and self.root.winfo_exists():
                            self.root.after_idle(lambda: self.create_photo_and_update(img_data, path, frame, row, col))
                except Exception as e:
                    print(f"无法加载图片 {path}: {str(e)}")
                    if hasattr(self.root, 'winfo_exists') and self.root.winfo_exists():
                        self.root.after_idle(lambda: self.update_thumbnail_error(path, frame, row, col))
        except Exception as e:
            print(f"处理文件时出错 {path}: {str(e)}")
            if hasattr(self.root, 'winfo_exists') and self.root.winfo_exists():
                self.root.after_idle(lambda: self.update_thumbnail_error(path, frame, row, col))
        finally:
            with self.load_lock:
                self.active_loads -= 1

    def create_video_thumbnail(self, img_data, path, frame, row, col):
        """在主线程中创建视频缩略图并更新UI"""
        try:
            import io
            from PIL import Image, ImageTk
            img = Image.open(io.BytesIO(img_data))
            photo = ImageTk.PhotoImage(img)
            
            if not frame.winfo_exists():
                return
            
            # 清除加载中标签
            for widget in frame.winfo_children():
                widget.destroy()
            
            # 添加图片标签
            label = ttk.Label(frame, image=photo)
            label.image = photo
            label.pack()
            
            # 创建信息框架
            info_frame = ttk.Frame(frame)
            info_frame.pack(fill=tk.X, padx=2)
            
            # 获取视频信息
            try:
                cap = cv2.VideoCapture(path)
                if cap.isOpened():
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    fps = int(cap.get(cv2.CAP_PROP_FPS))
                    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    duration = frame_count / fps if fps > 0 else 0
                    cap.release()
                    
                    # 添加视频信息标签
                    file_size = os.path.getsize(path)
                    size_str = self.format_file_size(file_size)
                    duration_str = f"{int(duration//60)}:{int(duration%60):02d}"
                    info_text = f"{width}x{height} {fps}fps\n{duration_str} {size_str}"
                    info_label = ttk.Label(info_frame, text=info_text)
                    info_label.pack(side=tk.LEFT)
                    
                    # 添加视频图标或标识
                    video_icon = ttk.Label(info_frame, text="▶ 视频", foreground="blue")
                    video_icon.pack(side=tk.LEFT, padx=(5, 0))
            except Exception as e:
                print(f"无法获取视频信息 {path}: {str(e)}")
                file_size = os.path.getsize(path)
                size_str = self.format_file_size(file_size)
                info_label = ttk.Label(info_frame, text=size_str)
                info_label.pack(side=tk.LEFT)
            
            # 添加勾选框到信息框架右侧
            var = tk.BooleanVar(value=path in self.selected_files)
            checkbox = ttk.Checkbutton(info_frame, variable=var,
                                     command=lambda p=path, v=var: self.toggle_selection(p, v))
            checkbox.pack(side=tk.RIGHT)

            # 改为单击事件
            label.bind('<Button-1>', lambda e, p=path: self.show_video_popup(p))

            # 添加右键菜单
            label.bind('<Button-3>', lambda e, p=path: self.show_context_menu(e, p))

            # 添加鼠标悬停效果
            label.bind('<Enter>', lambda e: label.configure(cursor='hand2'))
            label.bind('<Leave>', lambda e: label.configure(cursor=''))

            # 绑定滚轮事件到所有新创建的控件
            label.bind('<MouseWheel>', self.on_mousewheel)
            info_frame.bind('<MouseWheel>', self.on_mousewheel)
            checkbox.bind('<MouseWheel>', self.on_mousewheel)
            if 'info_label' in locals():
                info_label.bind('<MouseWheel>', self.on_mousewheel)
            if 'video_icon' in locals():
                video_icon.bind('<MouseWheel>', self.on_mousewheel)

            self.thumbnails.append(photo)
        except Exception as e:
            print(f"创建视频缩略图时出错 {path}: {str(e)}")
            self.update_thumbnail_error(path, frame, row, col)

    def create_photo_and_update(self, img_data, path, frame, row, col):
        """在主线程中创建PhotoImage并更新UI"""
        try:
            # 从字节流重建图像
            import io
            from PIL import Image, ImageTk
            img = Image.open(io.BytesIO(img_data))
            photo = ImageTk.PhotoImage(img)
            
            # 更新UI
            self.update_thumbnail(photo, path, frame, row, col)
        except Exception as e:
            print(f"在主线程创建图像时出错 {path}: {str(e)}")
            self.update_thumbnail_error(path, frame, row, col)
    
    def update_thumbnail(self, photo, path, frame, row, col):
        """在主线程中更新缩略图"""
        try:
            if not frame.winfo_exists():
                return
            
            # 清除加载中标签
            for widget in frame.winfo_children():
                widget.destroy()
            
            # 保存文件路径到frame
            frame.file_path = path
            
            # 添加图片标签
            label = ttk.Label(frame, image=photo)
            label.image = photo
            label.pack()
            
            # 创建信息框架
            info_frame = ttk.Frame(frame)
            info_frame.pack(fill=tk.X, padx=2)
            
            # 获取图片信息
            try:
                with Image.open(path) as img:
                    width, height = img.size
                    # 添加分辨率和文件大小标签
                    file_size = os.path.getsize(path)
                    size_str = self.format_file_size(file_size)
                    info_text = f"{width}x{height}\n{size_str}"
                    info_label = ttk.Label(info_frame, text=info_text)
                    info_label.pack(side=tk.LEFT)
            except Exception as e:
                print(f"无法获取图片信息 {path}: {str(e)}")
                file_size = os.path.getsize(path)
                size_str = self.format_file_size(file_size)
                info_label = ttk.Label(info_frame, text=size_str)
                info_label.pack(side=tk.LEFT)
            
            # 添加勾选框到信息框架右侧
            var = tk.BooleanVar(value=path in self.selected_files)
            checkbox = ttk.Checkbutton(info_frame, variable=var,
                                     command=lambda p=path, v=var: self.toggle_selection(p, v))
            checkbox.pack(side=tk.RIGHT)

            # 改为单击事件
            label.bind('<Button-1>', lambda e, p=path: self.show_image_popup(p))

            # 添加右键菜单
            label.bind('<Button-3>', lambda e, p=path: self.show_context_menu(e, p))

            # 添加鼠标悬停效果
            label.bind('<Enter>', lambda e: label.configure(cursor='hand2'))
            label.bind('<Leave>', lambda e: label.configure(cursor=''))

            # 绑定滚轮事件到所有新创建的控件
            label.bind('<MouseWheel>', self.on_mousewheel)
            info_frame.bind('<MouseWheel>', self.on_mousewheel)
            info_label.bind('<MouseWheel>', self.on_mousewheel)
            checkbox.bind('<MouseWheel>', self.on_mousewheel)

            self.thumbnails.append(photo)
        except (tk.TclError, RuntimeError):
            print(f"无法更新缩略图，组件可能已被销毁: {path}")
            return
    
    def update_thumbnail_error(self, path, frame, row, col, error_img=None):
        """在主线程中显示图片加载错误"""
        try:
            # 检查frame是否仍然存在
            if not frame.winfo_exists():
                return
                
            # 清除加载中标签
            for widget in frame.winfo_children():
                widget.destroy()
            
            # 添加错误标签
            if error_img is None:
                # 在主线程中创建错误占位图像
                try:
                    # 创建一个红色背景的图像作为错误指示
                    error_img = Image.new('RGB', self.thumbnail_size, color=(240, 200, 200))
                    error_photo = ImageTk.PhotoImage(error_img)
                    
                    error_label = ttk.Label(frame, image=error_photo)
                    error_label.image = error_photo  # 保持引用
                    error_label.pack()
                    
                    # 添加错误提示文本
                    error_text = ttk.Label(frame, text="图片损坏", foreground="red")
                    error_text.pack()
                except Exception as e:
                    print(f"创建错误占位图像失败: {str(e)}")
                    # 如果创建图像失败，使用纯文本
                    error_label = ttk.Label(frame, text="加载失败", foreground="red")
                    error_label.pack(pady=self.thumbnail_size[1]//2)
            else:
                # 使用传入的错误图像
                error_photo = ImageTk.PhotoImage(error_img)
                error_label = ttk.Label(frame, image=error_photo)
                error_label.image = error_photo  # 保持引用
                error_label.pack()
                
                # 添加错误提示文本
                error_text = ttk.Label(frame, text="图片损坏", foreground="red")
                error_text.pack()
            
            # 添加文件大小信息
            try:
                file_size = os.path.getsize(path)
                size_str = self.format_file_size(file_size)
                info_label = ttk.Label(frame, text=size_str)
                info_label.pack()
                
                # 添加文件名信息（截断过长的文件名）
                filename = os.path.basename(path)
                if len(filename) > 20:
                    filename = filename[:10] + "..." + filename[-7:]
                name_label = ttk.Label(frame, text=filename)
                name_label.pack()
                
                # 添加右键菜单，提供删除选项
                error_label.bind('<Button-3>', lambda e, p=path: self.show_context_menu(e, p))
                if 'error_text' in locals():
                    error_text.bind('<Button-3>', lambda e, p=path: self.show_context_menu(e, p))

                # 绑定滚轮事件到错误缩略图的所有控件
                error_label.bind('<MouseWheel>', self.on_mousewheel)
                info_label.bind('<MouseWheel>', self.on_mousewheel)
                name_label.bind('<MouseWheel>', self.on_mousewheel)
                if 'error_text' in locals():
                    error_text.bind('<MouseWheel>', self.on_mousewheel)
            except Exception as e:
                print(f"添加文件信息失败: {str(e)}")
        except (tk.TclError, RuntimeError) as e:
            # 捕获可能的Tkinter错误，表明组件已不存在
            print(f"无法更新错误缩略图，组件可能已被销毁: {path}, 错误: {str(e)}")
            return
    
    def show_image_popup(self, path):
        """显示图片查看窗口"""
        ImagePopup(self.root, path, self.image_paths, self)  # 传递self作为viewer参数
    
    def show_context_menu(self, event, path):
        """显示右键菜单"""
        menu = tk.Menu(self.root, tearoff=0)
        
        # 检查文件是否存在
        if os.path.exists(path):
            # 常用操作组
            menu.add_command(label="移动", command=lambda: self.move_single_file(path))
            menu.add_command(label="查看属性", command=lambda: self.show_file_properties(path))
            
            # 添加分隔线
            menu.add_separator()
            
            # 危险操作组
            menu.add_command(label="删除", command=lambda: self.delete_image(path), foreground='red')
        
        menu.post(event.x_root, event.y_root)

    def move_single_file(self, path):
        """移动单个文件"""
        # 清除当前选择
        self.selected_files.clear()
        # 添加当前文件到选择集合
        self.selected_files.add(path)
        # 调用现有的移动功能
        self.move_selected()
    
    def show_file_properties(self, path):
        """显示文件属性"""
        try:
            # 获取文件基本信息
            file_size = os.path.getsize(path)
            size_str = self.format_file_size(file_size)
            
            # 获取文件创建和修改时间
            create_time = os.path.getctime(path)
            modify_time = os.path.getmtime(path)
            
            # 格式化时间
            import datetime
            create_time_str = datetime.datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
            modify_time_str = datetime.datetime.fromtimestamp(modify_time).strftime('%Y-%m-%d %H:%M:%S')
            
            # 构建属性信息
            info = f"文件名: {os.path.basename(path)}\n"
            info += f"路径: {path}\n"
            info += f"大小: {size_str}\n"
            info += f"创建时间: {create_time_str}\n"
            info += f"修改时间: {modify_time_str}"
            
            # 显示属性对话框
            messagebox.showinfo("文件属性", info)
        except Exception as e:
            messagebox.showerror("错误", f"无法获取文件属性：{str(e)}")
    
    def delete_image(self, path):
        """删除指定路径的图片"""
        try:
            # 规范化路径
            norm_path = os.path.normpath(path)

            # 尝试删除文件，添加重试机制
            self._delete_file_with_retry(norm_path)

            # 从列表中移除
            if norm_path in self.image_paths:
                self.image_paths.remove(norm_path)
            if norm_path in self.all_image_paths:
                self.all_image_paths.remove(norm_path)
            # 从选中集合中移除
            self.selected_files.discard(norm_path)

            # 智能调整当前页面
            self.adjust_current_page_after_deletion()

            # 刷新显示（保持在当前页面）
            self.load_current_page()
            self.update_page_controls()

        except Exception as e:
            messagebox.showerror("错误", f"删除图片失败：{str(e)}")
            raise  # 重新抛出异常，让调用者知道删除失败

    def _delete_file_with_retry(self, file_path, max_retries=3):
        """带重试机制的文件删除"""
        import time

        for attempt in range(max_retries):
            try:
                # 尝试删除文件
                os.remove(file_path)
                return  # 删除成功，直接返回
            except PermissionError as e:
                if attempt < max_retries - 1:
                    # 如果不是最后一次尝试，等待一下再重试
                    time.sleep(0.5)

                    # 尝试强制关闭可能占用文件的进程（Windows）
                    if os.name == 'nt':
                        try:
                            # 尝试使用Windows API强制删除
                            import ctypes
                            from ctypes import wintypes

                            # 设置文件属性为普通文件
                            ctypes.windll.kernel32.SetFileAttributesW(file_path, 0x80)  # FILE_ATTRIBUTE_NORMAL

                            # 再次尝试删除
                            os.remove(file_path)
                            return
                        except:
                            pass
                    continue
                else:
                    # 最后一次尝试失败，抛出异常
                    raise PermissionError(f"无法删除文件，可能被其他程序占用：{file_path}")
            except Exception as e:
                # 其他类型的异常直接抛出
                raise e

    def remove_from_lists(self, norm_path):
        """从图片列表中移除指定路径，但不刷新显示"""
        # 从列表中移除
        if norm_path in self.image_paths:
            self.image_paths.remove(norm_path)
        if norm_path in self.all_image_paths:
            self.all_image_paths.remove(norm_path)
        # 从选中集合中移除
        self.selected_files.discard(norm_path)
        # 更新图片总数显示
        self.total_info.configure(text=f"共 {len(self.image_paths)} 张图片")

    def adjust_current_page_after_deletion(self):
        """删除图片后智能调整当前页面"""
        if not self.image_paths:
            # 如果没有图片了，保持当前页面不变（不跳到第一页）
            return

        # 计算总页数
        total_pages = max(1, math.ceil(len(self.image_paths) / self.items_per_page))

        # 如果当前页超过了总页数，调整到最后一页
        if self.current_page > total_pages:
            self.current_page = total_pages

        # 确保当前页至少为1
        self.current_page = max(1, self.current_page)

    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择图片文件夹")
        if folder_path:
            self.open_folder(folder_path)

    def open_folder(self, folder_path=None):
        """打开文件夹"""
        if folder_path is None:
            folder_path = filedialog.askdirectory(title='选择图片文件夹')

        if not folder_path:
            return

        self.current_folder = folder_path
        self.current_page = 1
        self.load_images()
    
    def load_images(self):
        """加载文件夹中的图片"""
        # 清除所有现有的缩略图和引用
        for widget in self.thumbnail_frame.winfo_children():
            if hasattr(widget, 'image'):
                widget.image = None  # 清除图片引用
            # 递归清除子组件的图片引用
            for child in widget.winfo_children():
                if hasattr(child, 'image'):
                    child.image = None
            widget.destroy()
        
        # 清空图片路径列表和缩略图列表
        if hasattr(self, 'thumbnails'):
            for thumb in self.thumbnails:
                if thumb:
                    thumb = None
            self.thumbnails.clear()
        
        self.all_image_paths.clear()
        self.image_paths.clear()
        self.resolution_cache.clear()  # 清空分辨率缓存
        
        # 清除所有 PhotoImage 对象
        for attr in dir(self):
            try:
                if isinstance(getattr(self, attr), (ImageTk.PhotoImage, Image.Image)):
                    setattr(self, attr, None)
            except:
                pass
        
        # 强制进行垃圾回收
        gc.collect()
        gc.collect()  # 进行两次垃圾回收以确保所有循环引用都被清理
        
        # 重置滚动条位置到顶部
        self.canvas.yview_moveto(0)
        
        # 只有在当前文件夹存在且有效时才加载图片
        if self.current_folder and os.path.exists(self.current_folder):
            try:
                # 获取所有图片文件
                files = [f for f in os.listdir(self.current_folder) 
                        if os.path.isfile(os.path.join(self.current_folder, f)) 
                        and f.lower().endswith(self.supported_formats)]
                
                # 添加完整路径到图片列表
                for file in files:
                    file_path = os.path.join(self.current_folder, file)
                    # 规范化路径
                    file_path = os.path.normpath(file_path)
                    self.all_image_paths.append(file_path)
                    
            except PermissionError:
                messagebox.showerror("错误", f"无法访问文件夹：{self.current_folder}")
                self.current_folder = None
        
        # 更新顶部信息显示
        formatted_path = self.format_folder_path(self.current_folder)
        self.folder_info.configure(text=f"当前文件夹: {formatted_path}")
        
        # 更新图片总数显示
        self.total_info.configure(text=f"共 {len(self.all_image_paths)} 张图片")
        

        
        # 重置页码
        self.current_page = 1
        
        # 应用排序和过滤
        self.sort_and_filter_images()
    
    def update_page_controls(self):
        """更新分页控制按钮状态"""
        if not self.image_paths:
            self.prev_button.state(['disabled'])
            self.next_button.state(['disabled'])
            self.jump_button.state(['disabled'])
            self.page_entry.state(['disabled'])
            self.total_label.configure(text="/1页")
            self.page_var.set("1")
            return
            
        # 固定每页60个图片
        self.items_per_page = 60
        total_pages = max(1, math.ceil(len(self.image_paths) / self.items_per_page))
        
        # 更新页码显示
        self.total_label.configure(text=f"/{total_pages}页")
        self.page_var.set(str(self.current_page))
        
        # 更新按钮状态
        self.prev_button.state(['!disabled'] if self.current_page > 1 else ['disabled'])
        self.next_button.state(['!disabled'] if self.current_page < total_pages else ['disabled'])
        self.jump_button.state(['!disabled'])
        self.page_entry.state(['!disabled'])
    
    def prev_page(self):
        """显示上一页"""
        if self.is_loading:
            return
        if self.current_page > 1:
            # 清空加载队列
            with self.load_lock:
                self.loading_queue.clear()
                self.active_loads = 0
            
            self.current_page -= 1
            self.load_current_page()
            self.update_page_controls()
            
            # 将滚动条定位到顶部
            self.canvas.yview_moveto(0)
    
    def next_page(self):
        """显示下一页"""
        if self.is_loading:
            return
        rows, cols = self.calculate_grid()
        self.items_per_page = rows * cols
        total_pages = math.ceil(len(self.image_paths) / self.items_per_page)
        if self.current_page < total_pages:
            # 清空加载队列
            with self.load_lock:
                self.loading_queue.clear()
                self.active_loads = 0
            
            self.current_page += 1
            self.load_current_page()
            self.update_page_controls()
            
            # 将滚动条定位到顶部
            self.canvas.yview_moveto(0)
    
    def jump_to_page(self):
        """跳转到指定页面"""
        if self.is_loading:
            return
            
        try:
            page = int(self.page_var.get())
            total_pages = math.ceil(len(self.image_paths) / self.items_per_page)
            
            if 1 <= page <= total_pages:
                # 清空加载队列
                with self.load_lock:
                    self.loading_queue.clear()
                    self.active_loads = 0
                
                self.current_page = page
                self.load_current_page()
                self.update_page_controls()
                
                # 将滚动条定位到顶部
                self.canvas.yview_moveto(0)
            else:
                self.page_var.set(str(self.current_page))
        except ValueError:
            self.page_var.set(str(self.current_page))
    
    def run(self):
        # 绑定窗口大小改变事件
        self.root.bind('<Configure>', lambda e: self.on_window_resize())
        self.root.mainloop()
    
    def on_window_resize(self):
        """窗口大小改变时重新加载当前页"""
        if hasattr(self, '_resize_timer'):
            self.root.after_cancel(self._resize_timer)
        # 增加延迟时间，减少刷新频率
        self._resize_timer = self.root.after(500, self.reload_current_page)
    
    def reload_current_page(self):
        """重新加载当前页"""
        if self.image_paths:
            old_rows, old_cols = getattr(self, 'current_rows', 0), getattr(self, 'current_cols', 0)
            new_rows, new_cols = self.calculate_grid()
            
            # 只有当行列数发生变化时才重新加载
            if new_rows != old_rows or new_cols != old_cols:
                self.update_page_controls()
                self.load_current_page()
    
    def refresh_images(self):
        """刷新图片显示"""
        self.current_page = 1
        self.load_current_page()
        self.update_page_controls()
        
        # 将滚动条定位到顶部
        self.canvas.yview_moveto(0)
        
        # 更新图片总数显示
        self.total_info.configure(text=f"共 {len(self.image_paths)} 张图片")



    def on_closing(self):
        """窗口关闭时的处理"""
        # 停止所有加载任务
        with self.load_lock:
            self.loading_queue.clear()
            # 设置标志，通知线程停止
            self.is_loading = False
        
        # 清理所有图片资源
        for widget in self.thumbnail_frame.winfo_children():
            if hasattr(widget, 'image'):
                widget.image = None
            for child in widget.winfo_children():
                if hasattr(child, 'image'):
                    child.image = None
            widget.destroy()
        
        # 清空缩略图列表
        self.thumbnails.clear()
        
        # 清除所有 PhotoImage 对象
        for attr in dir(self):
            try:
                if isinstance(getattr(self, attr), (ImageTk.PhotoImage, Image.Image)):
                    setattr(self, attr, None)
            except:
                pass
        
        # 强制进行垃圾回收
        gc.collect()
        gc.collect()
        
        # 尝试手动释放内存
        if hasattr(os, 'system'):
            try:
                if os.name == 'nt':  # Windows系统
                    import ctypes
                    ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
                elif os.name == 'posix':  # Linux/Unix/Mac系统
                    os.system('sync')
            except:
                pass
        
        self.root.destroy()

    def has_images_in_folder(self, folder_path):
        """检查文件夹是否包含图片"""
        try:
            for file in os.listdir(folder_path):
                file_path = os.path.join(folder_path, file)
                if os.path.isfile(file_path) and file.lower().endswith(self.supported_formats):
                    return True
        except (PermissionError, OSError):
            pass
        return False

    def find_first_folder_with_images(self, start_folder):
        """递归查找第一个包含图片的子文件夹"""
        try:
            # 首先检查当前文件夹
            if self.has_images_in_folder(start_folder):
                return start_folder

            # 遍历所有子文件夹
            for item in os.listdir(start_folder):
                item_path = os.path.join(start_folder, item)
                if os.path.isdir(item_path):
                    result = self.find_first_folder_with_images(item_path)
                    if result:
                        return result
        except (PermissionError, OSError):
            pass
        return None











    def get_image_resolution(self, image_path):
        """获取图片或视频分辨率，带缓存功能"""
        if image_path in self.resolution_cache:
            return self.resolution_cache[image_path]
        
        try:
            # 检查是否是视频文件
            if os.path.splitext(image_path)[1].lower() in self.video_formats:
                cap = cv2.VideoCapture(image_path)
                if cap.isOpened():
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    cap.release()
                    resolution = (width, height)
                    self.resolution_cache[image_path] = resolution
                    return resolution
                else:
                    self.resolution_cache[image_path] = (0, 0)
                    return (0, 0)
            else:
                # 图片文件处理
                with Image.open(image_path) as img:
                    resolution = img.size
                    self.resolution_cache[image_path] = resolution
                    return resolution
        except Exception as e:
            print(f"无法获取文件分辨率 {image_path}: {str(e)}")
            self.resolution_cache[image_path] = (0, 0)
            return (0, 0)

    def apply_resolution_filter(self):
        """应用分辨率过滤"""
        # 重新加载图片（应用过滤器）
        self.current_page = 1
        self.filter_and_load_images()
    
    def filter_and_load_images(self):
        """根据分辨率过滤图片并加载"""
        # 调用新的排序和过滤方法
        self.sort_and_filter_images()
    
    def apply_sort(self):
        """应用排序设置"""
        self.current_page = 1
        self.sort_and_filter_images()
    
    def sort_and_filter_images(self):
        """根据排序和过滤条件处理图片"""
        # 先应用分辨率过滤
        selected_filter_value = self.resolution_filter_var.get()
        filter_mode = self.filter_mode_var.get()
        
        # 初始化过滤后的图片列表
        filtered_images = self.all_image_paths.copy()
        is_filtered = False
        filter_info = []
        
        # 应用分辨率过滤
        if selected_filter_value != "none":
            is_filtered = True
            # 获取选中的过滤条件
            selected_filter = None
            for filter_option in self.resolution_filters:
                if filter_option["value"] == selected_filter_value:
                    selected_filter = filter_option
                    break
            
            if selected_filter:
                threshold_pixels = selected_filter["pixels"]
                temp_images = []
                
                for path in filtered_images:
                    width, height = self.get_image_resolution(path)
                    total_pixels = width * height
                    
                    if filter_mode == "below" and total_pixels <= threshold_pixels:
                        temp_images.append(path)
                    elif filter_mode == "above" and total_pixels >= threshold_pixels:
                        temp_images.append(path)
                
                filtered_images = temp_images
                filter_info.append(f"{selected_filter['name']}{filter_mode == 'below' and '以下' or '以上'}")
        
        # 应用文件大小过滤
        if self.size_filter_enabled.get():
            is_filtered = True
            size_mode = self.size_filter_mode_var.get()
            temp_images = []
            
            for path in filtered_images:
                file_size = os.path.getsize(path)
                if size_mode == "below" and file_size <= self.size_threshold:
                    temp_images.append(path)
                elif size_mode == "above" and file_size >= self.size_threshold:
                    temp_images.append(path)
            
            filtered_images = temp_images
            filter_info.append(f"300KB{size_mode == 'below' and '以下' or '以上'}")
        
        # 更新图片列表
        self.image_paths = filtered_images
        
        # 应用排序
        sort_by = self.sort_by_var.get()
        sort_order = self.sort_order_var.get()
        
        if sort_by == "name":
            # 按文件名排序
            self.image_paths.sort(key=lambda x: os.path.basename(x).lower(),
                                reverse=(sort_order == "desc"))
        else:  # sort_by == "time"
            # 按修改时间排序
            self.image_paths.sort(key=lambda x: os.path.getmtime(x),
                                reverse=(sort_order == "desc"))
        
        # 更新页面控制
        self.update_page_controls()
        
        # 更新图片总数显示
        filtered_count = len(self.image_paths)
        total_count = len(self.all_image_paths)
        
        # 构建过滤信息文本
        if is_filtered:
            filter_text = "，".join(filter_info)
            self.total_info.configure(text=f"已过滤({filter_text}): {filtered_count}/{total_count} 张图片")
        else:
            self.total_info.configure(text=f"共 {total_count} 张图片")
        
        # 加载当前页
        self.load_current_page()

    def apply_filters(self):
        """应用所有过滤器"""
        self.current_page = 1
        self.sort_and_filter_images()

    def toggle_selection(self, path, var):
        """切换文件的选择状态"""
        if self.shift_pressed and self.last_selected:
            # 获取当前页的所有文件
            start_idx = (self.current_page - 1) * self.items_per_page
            end_idx = min(start_idx + self.items_per_page, len(self.image_paths))
            current_page_files = self.image_paths[start_idx:end_idx]
            
            # 找到上一次选择和当前选择的索引
            try:
                last_idx = current_page_files.index(self.last_selected)
                current_idx = current_page_files.index(path)
                
                # 确定选择范围
                start = min(last_idx, current_idx)
                end = max(last_idx, current_idx) + 1
                
                # 选择范围内的所有文件
                for i in range(start, end):
                    self.selected_files.add(current_page_files[i])
                    
                # 更新复选框状态
                for widget in self.thumbnail_frame.winfo_children():
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Frame):  # 信息框架
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk.Checkbutton):
                                    file_path = widget.file_path  # 假设我们在创建缩略图时保存了文件路径
                                    if file_path in self.selected_files:
                                        grandchild.state(['selected'])
                                    else:
                                        grandchild.state(['!selected'])
            except ValueError:
                # 如果找不到索引，就按普通选择处理
                if var.get():
                    self.selected_files.add(path)
                else:
                    self.selected_files.discard(path)
        else:
            # 普通选择模式
            if var.get():
                self.selected_files.add(path)
            else:
                self.selected_files.discard(path)
            self.last_selected = path
        
        self.update_selection_status()
    
    def update_selection_status(self):
        """更新选择状态显示"""
        total_files = len(self.image_paths)
        selected_count = len(self.selected_files)
        self.total_info.configure(text=f"{selected_count}/{total_files}")
    
    def select_all(self):
        """选择当前页面所有文件"""
        start_idx = (self.current_page - 1) * self.items_per_page
        end_idx = min(start_idx + self.items_per_page, len(self.image_paths))
        current_page_files = self.image_paths[start_idx:end_idx]
        
        for path in current_page_files:
            self.selected_files.add(path)
        
        # 刷新当前页面显示
        self.load_current_page()
        self.update_selection_status()

    def deselect_all(self):
        """取消选择所有文件"""
        self.selected_files.clear()
        # 刷新当前页面显示
        self.load_current_page()
        self.update_selection_status()

    def delete_selected(self):
        """删除选中的文件"""
        if not self.selected_files:
            messagebox.showinfo("提示", "请先选择要删除的文件")
            return
            
        if not messagebox.askyesno("确认删除", f"确定要删除选中的 {len(self.selected_files)} 个文件吗？\n此操作无法撤销！"):
            return
            
        # 记录删除失败的文件
        failed_files = []
        deleted_count = 0
        
        # 删除选中的文件
        for path in list(self.selected_files):  # 使用列表副本进行迭代
            try:
                # 规范化路径
                norm_path = os.path.normpath(path)

                # 使用重试机制删除文件
                self._delete_file_with_retry(norm_path)
                
                # 从列表中移除
                if norm_path in self.image_paths:
                    self.image_paths.remove(norm_path)
                if norm_path in self.all_image_paths:
                    self.all_image_paths.remove(norm_path)
                
                # 从选中集合中移除
                self.selected_files.discard(norm_path)
                
                deleted_count += 1
            except Exception as e:
                failed_files.append(f"{os.path.basename(path)}: {str(e)}")
        
        # 智能调整当前页面
        self.adjust_current_page_after_deletion()

        # 刷新显示（保持在当前页面）
        self.load_current_page()
        self.update_page_controls()
        

        
        # 显示结果消息
        if failed_files:
            messagebox.showwarning("删除结果", 
                f"成功删除 {deleted_count} 个文件，{len(failed_files)} 个文件删除失败。\n\n失败列表:\n" + 
                "\n".join(failed_files[:10]) + 
                ("..." if len(failed_files) > 10 else ""))
        else:
            messagebox.showinfo("删除成功", f"成功删除所有 {deleted_count} 个文件")

    def move_selected(self):
        """移动选中的文件"""
        if not self.selected_files:
            messagebox.showinfo("提示", "请先选择要移动的文件")
            return
            
        # 直接打开文件夹选择对话框
        target_path = filedialog.askdirectory(title='选择移动目标文件夹')

        # 如果没有选择目标路径，直接返回
        if not target_path:
            return
            
        try:


            # 记录移动失败的文件
            failed_files = []
            moved_count = 0

            # 添加覆盖选择状态
            overwrite_all = False  # 是否对所有文件都覆盖
            skip_all = False       # 是否跳过所有冲突文件

            # 移动选中的文件
            for path in list(self.selected_files):  # 使用列表副本进行迭代
                try:
                    # 获取文件名
                    file_name = os.path.basename(path)
                    # 构建目标路径
                    target_full_path = os.path.join(target_path, file_name)

                    # 检查目标路径是否已存在
                    if os.path.exists(target_full_path):
                        if skip_all:
                            continue  # 跳过所有冲突文件
                        elif not overwrite_all:
                            # 显示自定义确认对话框
                            choice, new_name = self.show_overwrite_dialog(file_name)
                            if choice == "cancel":
                                break  # 取消整个操作
                            elif choice == "no":
                                continue  # 跳过当前文件
                            elif choice == "no_all":
                                skip_all = True
                                continue  # 跳过当前文件并设置跳过所有
                            elif choice == "yes_all":
                                overwrite_all = True
                                # 继续执行移动操作
                            elif choice == "rename":
                                # 使用新文件名
                                file_name = new_name
                                target_full_path = os.path.join(target_path, file_name)
                                # 检查新文件名是否也冲突
                                if os.path.exists(target_full_path):
                                    # 如果新文件名也冲突，生成唯一文件名
                                    file_name = self._generate_unique_filename(target_path, new_name)
                                    target_full_path = os.path.join(target_path, file_name)
                            elif choice == "auto_rename":
                                # 自动生成不冲突的文件名
                                file_name = self._generate_unique_filename(target_path, file_name)
                                target_full_path = os.path.join(target_path, file_name)
                            # choice == "yes" 时直接继续执行移动操作

                    # 移动文件
                    import shutil
                    shutil.move(path, target_full_path)

                    # 从列表中移除
                    if path in self.image_paths:
                        self.image_paths.remove(path)
                    if path in self.all_image_paths:
                        self.all_image_paths.remove(path)

                    # 从选中集合中移除
                    self.selected_files.discard(path)

                    moved_count += 1
                except Exception as e:
                    failed_files.append(f"{os.path.basename(path)}: {str(e)}")
            
            # 智能调整当前页面
            self.adjust_current_page_after_deletion()

            # 刷新显示（保持在当前页面）
            self.load_current_page()
            self.update_page_controls()
            

            
            # 显示结果消息
            if failed_files:
                messagebox.showwarning("移动结果", 
                    f"成功移动 {moved_count} 个文件，{len(failed_files)} 个文件移动失败。\n\n失败列表:\n" + 
                    "\n".join(failed_files[:10]) + 
                    ("..." if len(failed_files) > 10 else ""))
            else:
                messagebox.showinfo("移动成功", f"成功移动所有 {moved_count} 个文件到 {target_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"移动文件失败：{str(e)}")

    def show_overwrite_dialog(self, file_name, is_folder=False):
        """显示文件/文件夹覆盖确认对话框，返回用户选择和新文件名"""
        dialog = tk.Toplevel(self.root)
        dialog.title("文件夹冲突" if is_folder else "文件冲突")
        dialog.geometry("450x250")
        dialog.transient(self.root)
        dialog.grab_set()  # 模态对话框

        # 居中显示
        dialog.geometry("+%d+%d" % (
            self.root.winfo_rootx() + self.root.winfo_width()//2 - 225,
            self.root.winfo_rooty() + self.root.winfo_height()//2 - 125
        ))

        # 用于存储用户选择的变量
        result = {"choice": "cancel", "new_name": file_name}

        # 创建主框架
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 提示信息
        item_type = "文件夹" if is_folder else "文件"
        message_label = ttk.Label(
            main_frame,
            text=f"{item_type} '{file_name}' 在目标位置已存在。\n您希望如何处理？",
            justify=tk.CENTER
        )
        message_label.pack(pady=(0, 15))

        # 重命名输入框架
        rename_frame = ttk.LabelFrame(main_frame, text="重命名选项")
        rename_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(rename_frame, text=f"新{item_type}名:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        name_var = tk.StringVar(value=file_name)
        name_entry = ttk.Entry(rename_frame, textvariable=name_var, width=50)
        name_entry.pack(fill=tk.X, padx=5, pady=(0, 5))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # 创建按钮处理函数
        def on_yes():
            result["choice"] = "yes"
            dialog.destroy()

        def on_yes_all():
            result["choice"] = "yes_all"
            dialog.destroy()

        def on_no():
            result["choice"] = "no"
            dialog.destroy()

        def on_no_all():
            result["choice"] = "no_all"
            dialog.destroy()

        def on_rename():
            new_name = name_var.get().strip()
            if new_name and new_name != file_name:
                result["choice"] = "rename"
                result["new_name"] = new_name
                dialog.destroy()
            else:
                messagebox.showwarning("警告", f"请输入一个不同的{item_type}名")

        def on_auto_rename():
            # 自动生成不冲突的文件名
            result["choice"] = "auto_rename"
            result["new_name"] = file_name  # 原始名称，后续会自动处理
            dialog.destroy()

        def on_cancel():
            result["choice"] = "cancel"
            dialog.destroy()

        # 第一行按钮
        top_button_frame = ttk.Frame(button_frame)
        top_button_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(top_button_frame, text="覆盖", command=on_yes, width=10).pack(side=tk.LEFT, padx=2)
        if not is_folder:  # 文件夹移动通常是单个操作，不需要"全部"选项
            ttk.Button(top_button_frame, text="全部覆盖", command=on_yes_all, width=10).pack(side=tk.LEFT, padx=2)
        ttk.Button(top_button_frame, text="跳过", command=on_no, width=10).pack(side=tk.LEFT, padx=2)
        if not is_folder:
            ttk.Button(top_button_frame, text="全部跳过", command=on_no_all, width=10).pack(side=tk.LEFT, padx=2)

        # 第二行按钮
        bottom_button_frame = ttk.Frame(button_frame)
        bottom_button_frame.pack(fill=tk.X)

        ttk.Button(bottom_button_frame, text="重命名", command=on_rename, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(bottom_button_frame, text="自动重命名", command=on_auto_rename, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(bottom_button_frame, text="取消", command=on_cancel, width=10).pack(side=tk.RIGHT, padx=2)

        # 绑定回车键到重命名
        name_entry.bind('<Return>', lambda e: on_rename())
        # 绑定ESC键到取消
        dialog.bind('<Escape>', lambda e: on_cancel())

        # 选中文件名（不包括扩展名，对于文件夹则选中全部）
        if is_folder:
            name_entry.selection_range(0, len(file_name))
        else:
            name_only = os.path.splitext(file_name)[0]
            name_entry.selection_range(0, len(name_only))
        name_entry.focus()

        # 等待对话框关闭
        self.root.wait_window(dialog)

        return result["choice"], result["new_name"]

    def _generate_unique_filename(self, target_dir, filename):
        """生成唯一的文件名，避免冲突"""
        name, ext = os.path.splitext(filename)
        counter = 1

        while True:
            new_filename = f"{name}-{counter}{ext}"
            if not os.path.exists(os.path.join(target_dir, new_filename)):
                return new_filename

            counter += 1
            # 防止无限循环
            if counter > 1000:
                import time
                timestamp = int(time.time())
                return f"{name}_{timestamp}{ext}"

    def show_video_popup(self, path):
        """使用系统默认播放器打开视频文件"""
        try:
            # 规范化路径
            norm_path = os.path.normpath(path)
            
            if os.name == 'nt':  # Windows系统
                os.startfile(norm_path)
            else:  # Linux/Mac系统
                import subprocess
                subprocess.run(['xdg-open', norm_path])
                
        except Exception as e:
            messagebox.showerror("错误", f"无法打开视频文件：{str(e)}")

    def on_shift_press(self, event):
        """处理Shift键按下事件"""
        self.shift_pressed = True

    def on_shift_release(self, event):
        """处理Shift键释放事件"""
        self.shift_pressed = False

    def on_up_key(self, event):
        """处理上键事件 - 切换到上一个文件夹"""
        if not self.current_folder:
            return

        # 获取当前文件夹的兄弟文件夹列表
        parent_path = os.path.dirname(self.current_folder)
        siblings = self._get_sibling_folders(self.current_folder)

        if siblings:
            current_name = os.path.basename(self.current_folder)
            try:
                current_index = siblings.index(current_name)
                if current_index > 0:
                    # 切换到上一个文件夹
                    prev_folder = os.path.join(parent_path, siblings[current_index - 1])
                    self._switch_to_folder(prev_folder)
            except ValueError:
                pass

    def on_down_key(self, event):
        """处理下键事件 - 切换到下一个文件夹"""
        if not self.current_folder:
            return

        # 获取当前文件夹的兄弟文件夹列表
        parent_path = os.path.dirname(self.current_folder)
        siblings = self._get_sibling_folders(self.current_folder)

        if siblings:
            current_name = os.path.basename(self.current_folder)
            try:
                current_index = siblings.index(current_name)
                if current_index < len(siblings) - 1:
                    # 切换到下一个文件夹
                    next_folder = os.path.join(parent_path, siblings[current_index + 1])
                    self._switch_to_folder(next_folder)
            except ValueError:
                pass

    def _get_sibling_folders(self, folder_path):
        """获取指定文件夹的兄弟文件夹列表"""
        try:
            parent_path = os.path.dirname(folder_path)
            if not os.path.exists(parent_path):
                return []

            siblings = []
            for item in os.listdir(parent_path):
                item_path = os.path.join(parent_path, item)
                if os.path.isdir(item_path):
                    siblings.append(item)

            # 按字母顺序排序
            siblings.sort()
            return siblings
        except Exception as e:
            return []

    def _switch_to_folder(self, folder_path):
        """切换到指定文件夹"""
        try:
            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                # 查找包含图片的文件夹
                image_folder = self.find_first_folder_with_images(folder_path)
                target_folder = image_folder if image_folder else folder_path

                # 更新当前文件夹
                self.current_folder = target_folder
                self.current_page = 1

                # 加载图片
                self.load_images()
        except Exception as e:
            print(f"切换文件夹失败: {str(e)}")



if __name__ == "__main__":
    import sys
    # 检查是否有命令行参数传入的文件夹路径
    initial_folder = None
    if len(sys.argv) > 1:
        initial_folder = sys.argv[1]

    viewer = ImageViewer(initial_folder)
    viewer.run()
