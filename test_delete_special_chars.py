# -*- coding: utf-8 -*-

import os
import tempfile
import sys
sys.path.append('.')

# 导入删除函数
from 短视频预览播放 import delete_file_with_system

def test_delete_special_chars():
    """测试删除包含特殊字符的文件"""
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    print(f"临时目录: {temp_dir}")
    
    # 测试文件名（包含特殊字符）
    test_files = [
        "normal_file.txt",
        "file+with+plus.txt", 
        "file with spaces.txt",
        "file&with&ampersand.txt",
        "file(with)parentheses.txt",
        "file[with]brackets.txt",
        "file{with}braces.txt",
        "file@<EMAIL>",
        "file#with#hash.txt",
        "file$with$dollar.txt",
        "file%with%percent.txt"
    ]
    
    created_files = []
    
    try:
        # 创建测试文件
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"测试文件: {filename}")
                created_files.append(file_path)
                print(f"✓ 创建文件: {filename}")
            except Exception as e:
                print(f"✗ 创建文件失败: {filename}, 错误: {e}")
        
        print(f"\n成功创建 {len(created_files)} 个测试文件")
        
        # 测试删除文件
        success_count = 0
        for file_path in created_files:
            filename = os.path.basename(file_path)
            try:
                if os.path.exists(file_path):
                    delete_file_with_system(file_path)
                    if not os.path.exists(file_path):
                        print(f"✓ 成功删除: {filename}")
                        success_count += 1
                    else:
                        print(f"✗ 删除失败: {filename} (文件仍存在)")
                else:
                    print(f"✗ 文件不存在: {filename}")
            except Exception as e:
                print(f"✗ 删除失败: {filename}, 错误: {e}")
        
        print(f"\n删除结果: {success_count}/{len(created_files)} 个文件成功删除")
        
    finally:
        # 清理剩余文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
            print(f"✓ 清理临时目录: {temp_dir}")
        except Exception as e:
            print(f"✗ 清理临时目录失败: {e}")

if __name__ == "__main__":
    print("开始测试删除包含特殊字符的文件...")
    test_delete_special_chars()
    print("测试完成")
